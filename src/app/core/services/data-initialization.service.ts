import { Injectable, inject } from '@angular/core';
import { Observable, from, switchMap, map, of, forkJoin } from 'rxjs';
import { Firestore, doc, setDoc, updateDoc, Timestamp } from '@angular/fire/firestore';
import { UserProfile } from '../auth/auth.service';
import { BusinessProfile } from '../models/business.model';
import { FirebaseContextService } from './firebase-context.service';

/**
 * Data Initialization Service
 *
 * Handles the creation of initial data for new users including:
 * - Business profiles
 * - Staff profiles
 * - Sample data for testing
 */
@Injectable({
  providedIn: 'root'
})
export class DataInitializationService {
  private firestore = inject(Firestore);
  private firebaseContext = inject(FirebaseContextService);

  /**
   * Initialize all necessary data for a new user
   */
  initializeNewUser(userProfile: UserProfile): Observable<void> {
    console.log('🚀 Initializing data for new user:', userProfile.email);

    return this.createBusinessProfile(userProfile).pipe(
      switchMap(businessProfile => {
        return this.createStaffProfile(userProfile, businessProfile).pipe(
          switchMap(staffProfile => {
            return this.updateUserProfileWithIds(userProfile, businessProfile.id, staffProfile.id);
          })
        );
      })
    );
  }

  /**
   * Create a default business profile for the user
   */
  private createBusinessProfile(userProfile: UserProfile): Observable<BusinessProfile> {
    const businessId = this.generateId();
    const now = new Date();

    const businessProfile: BusinessProfile = {
      id: businessId,
      name: `${userProfile.displayName}'s Business`,
      displayName: `${userProfile.displayName}'s Business`,
      description: 'Default business profile created automatically',
      businessType: 'other',
      industry: 'General',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'US'
      },
      hoursOfOperation: this.createDefaultHours(),
      hoursOfBusiness: this.createDefaultBusinessHours(),
      operationalSettings: this.createDefaultOperationalSettings(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      locale: 'en-US',
      currency: 'USD',
      staffSettings: this.createDefaultStaffSettings(),
      schedulingSettings: this.createDefaultSchedulingSettings(),
      compliance: this.createDefaultCompliance(),
      createdAt: now,
      updatedAt: now,
      createdBy: userProfile.uid,
      ownerId: userProfile.uid,
      status: 'active'
    };

    return this.firebaseContext.setDocument(`businessProfiles/${businessId}`, businessProfile).pipe(
      map(() => {
        console.log('✅ Business profile created:', businessId);
        return businessProfile;
      })
    );
  }

  /**
   * Create a staff profile for the user
   */
  private createStaffProfile(userProfile: UserProfile, businessProfile: BusinessProfile): Observable<any> {
    const staffId = this.generateId();
    const now = new Date();

    const [firstName, ...lastNameParts] = userProfile.displayName.split(' ');
    const lastName = lastNameParts.join(' ') || 'User';

    const staffProfile: any = {
      id: staffId,
      employeeId: `EMP-${Date.now()}`,
      firstName,
      lastName,
      email: userProfile.email,
      phone: '',
      hireDate: now,
      avatar: userProfile.photoURL,
      position: userProfile.role === 'admin' ? 'Administrator' :
                userProfile.role === 'manager' ? 'Manager' : 'Staff Member',
      department: 'General',
      employmentType: 'full-time',
      status: 'active',
      skills: [],
      certifications: [],
      education: [],
      workExperience: [],
      availability: this.createDefaultAvailability(),
      timeZone: businessProfile.timeZone,
      createdAt: now,
      updatedAt: now,
      createdBy: userProfile.uid,
      businessIds: [businessProfile.id],
      primaryBusinessId: businessProfile.id,
      roles: [userProfile.role],
      permissions: this.getDefaultPermissions(userProfile.role),
      accessLevel: userProfile.role === 'admin' ? 'admin' :
                   userProfile.role === 'manager' ? 'advanced' : 'basic',
      tags: ['new-user']
    };

    return this.firebaseContext.setDocument(`staff/${staffId}`, staffProfile).pipe(
      map(() => {
        console.log('✅ Staff profile created:', staffId);
        return staffProfile;
      })
    );
  }

  /**
   * Update user profile with business and staff IDs
   */
  private updateUserProfileWithIds(userProfile: UserProfile, businessId: string, staffId: string): Observable<void> {
    const updates = {
      businessIds: [businessId],
      primaryBusinessId: businessId,
      staffId: staffId,
      updatedAt: new Date()
    };

    return this.firebaseContext.updateDocument(`users/${userProfile.uid}`, updates).pipe(
      map(() => {
        console.log('✅ User profile updated with business and staff IDs');
      })
    );
  }

  /**
   * Generate a unique ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Create default hours of operation
   */
  private createDefaultHours(): any {
    const defaultDay = { isOpen: true, openTime: '09:00', closeTime: '17:00' };
    return {
      monday: defaultDay,
      tuesday: defaultDay,
      wednesday: defaultDay,
      thursday: defaultDay,
      friday: defaultDay,
      saturday: { isOpen: true, openTime: '10:00', closeTime: '16:00' },
      sunday: { isOpen: false },
      holidays: [],
      specialDates: [],
      displaySettings: {
        showOnWebsite: true,
        showToCustomers: true,
        autoUpdateStatus: true
      }
    };
  }

  /**
   * Create default business hours
   */
  private createDefaultBusinessHours(): any {
    const defaultDay = { isOpen: true, openTime: '08:00', closeTime: '18:00' };
    return {
      operationalHours: {
        monday: defaultDay,
        tuesday: defaultDay,
        wednesday: defaultDay,
        thursday: defaultDay,
        friday: defaultDay,
        saturday: defaultDay,
        sunday: { isOpen: false }
      },
      staffSchedulingWindow: {
        earliestStart: '06:00',
        latestEnd: '22:00',
        allowOvernightShifts: false
      }
    };
  }

  /**
   * Create default operational settings
   */
  private createDefaultOperationalSettings(): any {
    return {
      breakPolicies: {
        minimumShiftForBreak: 4,
        breakDuration: 15,
        mealBreakDuration: 30,
        minimumShiftForMeal: 6,
        maxHoursWithoutBreak: 5
      },
      overtimePolicies: {
        dailyOvertimeThreshold: 8,
        weeklyOvertimeThreshold: 40,
        overtimeMultiplier: 1.5,
        requireApprovalForOvertime: true
      }
    };
  }

  /**
   * Create default staff settings
   */
  private createDefaultStaffSettings(): any {
    return {
      scheduleAdvanceNotice: 7,
      allowStaffSelfScheduling: false,
      allowShiftSwapping: true,
      requireManagerApproval: true,
      clockInMethod: 'manual',
      allowEarlyClockIn: true,
      earlyClockInThreshold: 15,
      allowLateClockOut: true,
      lateClockOutThreshold: 15,
      minimumAvailabilityHours: 20,
      requireWeekendAvailability: false,
      requireHolidayAvailability: false
    };
  }

  /**
   * Create default scheduling settings
   */
  private createDefaultSchedulingSettings(): any {
    return {
      enableAutoScheduling: false,
      autoSchedulingRules: [],
      defaultShiftLength: 8,
      minimumShiftLength: 2,
      maximumShiftLength: 12,
      minimumCoverageRules: [],
      schedulePublishDay: 0,
      notifyStaffOfChanges: true,
      notificationLeadTime: 24
    };
  }

  /**
   * Create default compliance settings
   */
  private createDefaultCompliance(): any {
    return {
      laborLaws: {
        jurisdiction: 'US-Federal',
        minimumWage: 7.25,
        overtimeRules: {},
        breakRequirements: {}
      },
      recordRetention: {
        timesheetRetentionDays: 1095,
        scheduleRetentionDays: 365,
        performanceRetentionDays: 2555
      }
    };
  }

  /**
   * Create default availability
   */
  private createDefaultAvailability(): any {
    const defaultDay = {
      isAvailable: true,
      timeSlots: [{ startTime: '09:00', endTime: '17:00' }]
    };

    return {
      weeklyAvailability: {
        monday: defaultDay,
        tuesday: defaultDay,
        wednesday: defaultDay,
        thursday: defaultDay,
        friday: defaultDay,
        saturday: { isAvailable: false, timeSlots: [] },
        sunday: { isAvailable: false, timeSlots: [] }
      },
      timeOffRequests: [],
      blackoutDates: [],
      preferredShifts: [],
      maxHoursPerWeek: 40,
      minHoursPerWeek: 20
    };
  }

  /**
   * Get default permissions based on role
   */
  private getDefaultPermissions(role: string): string[] {
    switch (role) {
      case 'admin':
        return ['all'];
      case 'manager':
        return ['manage_staff', 'view_reports', 'manage_schedules', 'approve_requests'];
      case 'staff':
        return ['view_own_profile', 'request_time_off', 'view_schedule'];
      default:
        return ['view_own_profile'];
    }
  }
}
