import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { Observable, take } from 'rxjs';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabsModule } from '@angular/material/tabs';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

// Services
import { StaffManagerThemeService } from '../../core/theme/staffmanager-theme';
import { SettingsService } from '../../core/services/settings.service';
import { AuthService } from '../../core/auth/auth.service';

// Models
import { UserSettings, SystemStatus, SettingsCategory } from '../../core/models/settings.model';

// Components
import { AppearanceSettingsComponent } from './components/appearance-settings.component';
import { SystemsCheckComponent } from './components/systems-check.component';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSlideToggleModule,
    MatTabsModule,
    MatListModule,
    MatDividerModule,
    MatChipsModule,
    MatBadgeModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    AppearanceSettingsComponent,
    SystemsCheckComponent
  ],
  template: `
    <div class="settings-container">
      <!-- Settings Header -->
      <div class="settings-header">
        <mat-card>
          <mat-card-header>
            <mat-icon mat-card-avatar>settings</mat-icon>
            <mat-card-title>Settings</mat-card-title>
            <mat-card-subtitle>Configure your StaffManager preferences and system settings</mat-card-subtitle>
          </mat-card-header>
        </mat-card>
      </div>

      <!-- Settings Content -->
      <div class="settings-content">
        <div class="settings-sidebar">
          <mat-card class="categories-card">
            <mat-card-header>
              <mat-card-title>Categories</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <mat-nav-list class="settings-nav">
                <mat-list-item
                  *ngFor="let category of settingsCategories"
                  [class.active]="selectedCategory?.id === category.id"
                  (click)="selectCategory(category)">
                  <mat-icon matListItemIcon>{{ category.icon }}</mat-icon>
                  <div matListItemTitle>{{ category.name }}</div>
                  <div matListItemLine>{{ category.description }}</div>
                  <mat-chip
                    *ngIf="category.id === 'systems' && (systemStatus$ | async)?.overall !== 'healthy'"
                    [color]="getStatusColor((systemStatus$ | async)?.overall || 'unknown')"
                    matListItemMeta>
                    {{ (systemStatus$ | async)?.overall }}
                  </mat-chip>
                </mat-list-item>
              </mat-nav-list>
            </mat-card-content>
          </mat-card>
        </div>

        <div class="settings-main">
          <!-- User Profile Section -->
          <div *ngIf="selectedCategory?.id === 'profile'" class="settings-section">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>person</mat-icon>
                <mat-card-title>User Profile Settings</mat-card-title>
                <mat-card-subtitle>Manage your personal account settings and staff profile</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="profile-actions">
                  <button mat-raised-button color="primary" (click)="viewUserProfile()">
                    <mat-icon>person</mat-icon>
                    View My Profile
                  </button>
                  <button mat-raised-button (click)="editUserProfile()">
                    <mat-icon>edit</mat-icon>
                    Edit My Profile
                  </button>
                </div>

                <mat-divider style="margin: 16px 0;"></mat-divider>

                <div *ngIf="userProfile$ | async as profile" class="profile-summary">
                  <h3>Current Profile Information</h3>
                  <div class="profile-info">
                    <div class="info-item">
                      <strong>Name:</strong> {{ profile.displayName }}
                    </div>
                    <div class="info-item">
                      <strong>Email:</strong> {{ profile.email }}
                    </div>
                    <div class="info-item">
                      <strong>Role:</strong> {{ profile.role | titlecase }}
                    </div>
                    <div class="info-item">
                      <strong>Member Since:</strong> {{ profile.createdAt | date:'mediumDate' }}
                    </div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Business Profiles Section -->
          <div *ngIf="selectedCategory?.id === 'business'" class="settings-section">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>business</mat-icon>
                <mat-card-title>Business Profiles</mat-card-title>
                <mat-card-subtitle>Manage multiple businesses and switch between them</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p>Business profile management coming soon. This will include:</p>
                <ul>
                  <li>Current business settings</li>
                  <li>Add, edit, or remove businesses</li>
                  <li>Switch between business contexts</li>
                  <li>Business-specific configurations</li>
                </ul>
                <div style="margin-top: 16px;">
                  <button mat-raised-button color="primary" routerLink="/business/profile">
                    <mat-icon>business</mat-icon>
                    Manage Current Business
                  </button>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Appearance Section -->
          <div *ngIf="selectedCategory?.id === 'appearance'" class="settings-section">
            <app-appearance-settings></app-appearance-settings>
          </div>

          <!-- Notifications Section -->
          <div *ngIf="selectedCategory?.id === 'notifications'" class="settings-section">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>notifications</mat-icon>
                <mat-card-title>Notification Settings</mat-card-title>
                <mat-card-subtitle>Configure email, push, and in-app notifications</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p>Notification settings coming soon. This will include:</p>
                <ul>
                  <li>Email notification preferences</li>
                  <li>Push notification settings</li>
                  <li>In-app notification center</li>
                  <li>Quiet hours and frequency controls</li>
                </ul>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Security Section -->
          <div *ngIf="selectedCategory?.id === 'security'" class="settings-section">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>security</mat-icon>
                <mat-card-title>Security & Privacy</mat-card-title>
                <mat-card-subtitle>Password, two-factor auth, and privacy controls</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p>Security settings coming soon. This will include:</p>
                <ul>
                  <li>Password management</li>
                  <li>Two-factor authentication</li>
                  <li>Privacy controls</li>
                  <li>Session management</li>
                </ul>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Time & Scheduling Section -->
          <div *ngIf="selectedCategory?.id === 'time'" class="settings-section">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>schedule</mat-icon>
                <mat-card-title>Time & Scheduling</mat-card-title>
                <mat-card-subtitle>Time zones, calendar, and scheduling preferences</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p>Time and scheduling settings coming soon. This will include:</p>
                <ul>
                  <li>Time zone and date format settings</li>
                  <li>Calendar view preferences</li>
                  <li>Default shift and scheduling options</li>
                  <li>Calendar integration settings</li>
                </ul>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Data Management Section -->
          <div *ngIf="selectedCategory?.id === 'data'" class="settings-section">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>storage</mat-icon>
                <mat-card-title>Data Management</mat-card-title>
                <mat-card-subtitle>Export, backup, and data retention settings</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p>Data management settings coming soon. This will include:</p>
                <ul>
                  <li>Export data in various formats</li>
                  <li>Automatic backup settings</li>
                  <li>Data retention policies</li>
                  <li>Import/export tools</li>
                </ul>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Integrations Section -->
          <div *ngIf="selectedCategory?.id === 'integrations'" class="settings-section">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>extension</mat-icon>
                <mat-card-title>Integrations</mat-card-title>
                <mat-card-subtitle>Connect with third-party services</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p>Integration settings coming soon. This will include:</p>
                <ul>
                  <li>Calendar apps (Google, Outlook, Apple)</li>
                  <li>Payroll systems (QuickBooks, ADP, Paychex)</li>
                  <li>Communication tools (Slack, Teams)</li>
                  <li>Time tracking integrations</li>
                </ul>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Systems Check Section -->
          <div *ngIf="selectedCategory?.id === 'systems'" class="settings-section">
            <app-systems-check></app-systems-check>
          </div>

          <!-- Advanced Section -->
          <div *ngIf="selectedCategory?.id === 'advanced'" class="settings-section">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>tune</mat-icon>
                <mat-card-title>Advanced Settings</mat-card-title>
                <mat-card-subtitle>Developer options and experimental features</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p>Advanced settings coming soon. This will include:</p>
                <ul>
                  <li>Developer options and debug mode</li>
                  <li>Feature flags and experimental features</li>
                  <li>Performance settings</li>
                  <li>Beta feature access</li>
                </ul>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Default/Welcome Section -->
          <div *ngIf="!selectedCategory" class="settings-welcome">
            <mat-card>
              <mat-card-header>
                <mat-icon mat-card-avatar>tune</mat-icon>
                <mat-card-title>Welcome to Settings</mat-card-title>
                <mat-card-subtitle>Choose a category from the sidebar to get started</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="quick-actions">
                  <h3>Quick Actions</h3>
                  <div class="action-buttons">
                    <button mat-raised-button color="primary" (click)="selectCategoryById('appearance')">
                      <mat-icon>palette</mat-icon>
                      Customize Appearance
                    </button>
                    <button mat-raised-button (click)="selectCategoryById('profile')">
                      <mat-icon>person</mat-icon>
                      Edit Profile
                    </button>
                    <button mat-raised-button (click)="selectCategoryById('systems')">
                      <mat-icon>health_and_safety</mat-icon>
                      System Status
                    </button>
                  </div>
                </div>

                <mat-divider></mat-divider>

                <div class="system-overview" *ngIf="systemStatus$ | async as status">
                  <h3>System Overview</h3>
                  <div class="status-grid">
                    <div class="status-item" *ngFor="let service of getServiceEntries(status.services)">
                      <mat-icon [class]="'status-' + service.value.status">
                        {{ getServiceIcon(service.key) }}
                      </mat-icon>
                      <span class="service-name">{{ getServiceName(service.key) }}</span>
                      <mat-chip [class]="'status-' + service.value.status">
                        {{ service.value.status }}
                      </mat-chip>
                    </div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>

      <!-- Sign Out Button (Always Visible) -->
      <div class="settings-footer">
        <mat-card>
          <mat-card-content>
            <div class="footer-actions">
              <button mat-raised-button color="warn" (click)="signOut()">
                <mat-icon>logout</mat-icon>
                Sign Out
              </button>
              <span class="footer-info">
                Last updated: {{ (userSettings$ | async)?.updatedAt | date:'short' }}
              </span>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .settings-container {
      padding: 24px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .settings-header {
      margin-bottom: 24px;

      mat-card {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;

        mat-icon {
          color: white;
        }
      }
    }

    .settings-content {
      display: grid;
      grid-template-columns: 320px 1fr;
      gap: 24px;
      min-height: 600px;
    }

    .settings-sidebar {
      .categories-card {
        position: sticky;
        top: 24px;

        .settings-nav {
          padding: 0;

          mat-list-item {
            margin-bottom: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;

            &:hover {
              background-color: rgba(25, 118, 210, 0.08);
            }

            &.active {
              background-color: rgba(25, 118, 210, 0.12);
              border-left: 4px solid #1976d2;
            }

            mat-icon {
              color: #1976d2;
            }

            mat-chip {
              font-size: 0.75rem;
              height: 20px;

              &.status-healthy {
                background-color: #4caf50;
                color: white;
              }

              &.status-warning {
                background-color: #ff9800;
                color: white;
              }

              &.status-error {
                background-color: #f44336;
                color: white;
              }
            }
          }
        }
      }
    }

    .settings-main {
      .settings-section {
        animation: fadeIn 0.3s ease-in-out;
      }

      .profile-actions {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        flex-wrap: wrap;

        button {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }

      .profile-summary {
        h3 {
          margin-bottom: 16px;
          color: #424242;
        }

        .profile-info {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 12px;

          .info-item {
            padding: 12px;
            background-color: #f5f5f5;
            border-radius: 8px;
            border-left: 4px solid #1976d2;

            strong {
              color: #424242;
              margin-right: 8px;
            }
          }
        }
      }

      .settings-welcome {
        .quick-actions {
          margin-bottom: 24px;

          h3 {
            margin-bottom: 16px;
            color: #424242;
          }

          .action-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;

            button {
              display: flex;
              align-items: center;
              gap: 8px;
            }
          }
        }

        .system-overview {
          margin-top: 24px;

          h3 {
            margin-bottom: 16px;
            color: #424242;
          }

          .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;

            .status-item {
              display: flex;
              align-items: center;
              gap: 12px;
              padding: 12px;
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background-color: #fafafa;

              mat-icon {
                &.status-online {
                  color: #4caf50;
                }

                &.status-offline {
                  color: #f44336;
                }

                &.status-degraded {
                  color: #ff9800;
                }

                &.status-maintenance {
                  color: #9e9e9e;
                }
              }

              .service-name {
                flex: 1;
                font-weight: 500;
              }

              mat-chip {
                font-size: 0.75rem;
                height: 24px;

                &.status-online {
                  background-color: #4caf50;
                  color: white;
                }

                &.status-offline {
                  background-color: #f44336;
                  color: white;
                }

                &.status-degraded {
                  background-color: #ff9800;
                  color: white;
                }

                &.status-maintenance {
                  background-color: #9e9e9e;
                  color: white;
                }
              }
            }
          }
        }
      }
    }

    .settings-footer {
      margin-top: 24px;

      .footer-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;

        button {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .footer-info {
          color: #666;
          font-size: 0.875rem;
        }
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    // Responsive design
    @media (max-width: 1024px) {
      .settings-content {
        grid-template-columns: 280px 1fr;
        gap: 16px;
      }
    }

    @media (max-width: 768px) {
      .settings-container {
        padding: 16px;
      }

      .settings-content {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .settings-sidebar {
        .categories-card {
          position: static;
        }
      }

      .settings-welcome {
        .action-buttons {
          flex-direction: column;

          button {
            width: 100%;
            justify-content: center;
          }
        }

        .system-overview {
          .status-grid {
            grid-template-columns: 1fr;
          }
        }
      }

      .settings-footer {
        .footer-actions {
          flex-direction: column;
          gap: 12px;
          text-align: center;
        }
      }
    }
  `]
})
export class SettingsComponent implements OnInit {
  private settingsService = inject(SettingsService);
  private authService = inject(AuthService);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);
  private dialog = inject(MatDialog);

  // Observables
  userSettings$!: Observable<UserSettings | null>;
  systemStatus$!: Observable<SystemStatus | null>;
  userProfile$ = this.authService.userProfile$;

  // Component State
  settingsCategories: SettingsCategory[] = [];
  selectedCategory: SettingsCategory | null = null;

  constructor(public themeService: StaffManagerThemeService) {}

  ngOnInit(): void {
    // Initialize observables
    this.userSettings$ = this.settingsService.userSettings$;
    this.systemStatus$ = this.settingsService.systemStatus$;

    // Load settings categories
    this.settingsCategories = this.settingsService.getSettingsCategories();

    // Select default category (appearance)
    this.selectCategoryById('appearance');
  }

  /**
   * Select a settings category
   */
  selectCategory(category: SettingsCategory): void {
    this.selectedCategory = category;
  }

  /**
   * Select category by ID
   */
  selectCategoryById(categoryId: string): void {
    const category = this.settingsCategories.find(c => c.id === categoryId);
    if (category) {
      this.selectCategory(category);
    }
  }

  /**
   * Get status color for chips
   */
  getStatusColor(status: string): 'primary' | 'accent' | 'warn' {
    switch (status) {
      case 'healthy':
        return 'primary';
      case 'warning':
        return 'accent';
      case 'error':
        return 'warn';
      default:
        return 'primary';
    }
  }

  /**
   * Get service entries for iteration
   */
  getServiceEntries(services: any): Array<{key: string, value: any}> {
    if (!services) return [];
    return Object.entries(services).map(([key, value]) => ({ key, value }));
  }

  /**
   * Get service icon
   */
  getServiceIcon(serviceKey: string): string {
    const icons: { [key: string]: string } = {
      ai: 'psychology',
      database: 'storage',
      pwa: 'install_mobile',
      auth: 'security',
      storage: 'folder',
      notifications: 'notifications'
    };
    return icons[serviceKey] || 'help';
  }

  /**
   * Get service display name
   */
  getServiceName(serviceKey: string): string {
    const names: { [key: string]: string } = {
      ai: 'AI Services',
      database: 'Database',
      pwa: 'PWA',
      auth: 'Authentication',
      storage: 'Storage',
      notifications: 'Notifications'
    };
    return names[serviceKey] || serviceKey;
  }

  /**
   * View user profile
   */
  viewUserProfile(): void {
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user) {
        this.router.navigate(['/staff', user.uid]);
      }
    });
  }

  /**
   * Edit user profile
   */
  editUserProfile(): void {
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user) {
        this.router.navigate(['/staff/edit', user.uid]);
      }
    });
  }

  /**
   * Sign out user
   */
  async signOut(): Promise<void> {
    try {
      await this.authService.signOut();
      this.snackBar.open('Successfully signed out', 'Close', { duration: 3000 });
      this.router.navigate(['/auth/login']);
    } catch (error) {
      this.snackBar.open('Error signing out', 'Close', { duration: 3000 });
    }
  }
}
