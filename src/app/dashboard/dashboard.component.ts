import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';
import { Subscription, Observable } from 'rxjs';
import { NumberWidgetComponent } from './widgets/number-widget.component';
import { UpcomingShiftsWidgetComponent } from './widgets/upcoming-shifts-widget.component';
import { DashboardStateService, DashboardWidget } from './services/dashboard-state.service';
import { WidgetDataService } from './services/widget-data.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, MatGridListModule, MatButtonModule, MatIconModule, DragDropModule, NumberWidgetComponent, UpcomingShiftsWidgetComponent],
  template: `
    <div class="dashboard-toolbar">
      <button mat-icon-button (click)="toggleEditMode()">
        <mat-icon>{{ editMode ? 'edit_off' : 'edit' }}</mat-icon>
      </button>
      <span *ngIf="editMode">Edit Mode Enabled</span>
    </div>
    <mat-grid-list cols="4" rowHeight="2:1" gutterSize="16px">
      <mat-grid-tile *ngFor="let widget of widgets" cdkDrag>
        <div class="widget-tile-header" *ngIf="editMode">
          <button mat-icon-button (click)="openWidgetSettings(widget)">
            <mat-icon>settings</mat-icon>
          </button>
        </div>
        <ng-container [ngSwitch]="widget.id">
          <app-number-widget
            *ngSwitchCase="'staff-on-shift'"
            [title]="'Staff On Shift'"
            [value]="(staffOnShift$ | async) ?? 0"
            [description]="'Staff members currently on shift'"
          ></app-number-widget>
          <app-upcoming-shifts-widget
            *ngSwitchCase="'upcoming-shifts'"
            [title]="'Upcoming Shifts'"
            [value]="((upcomingShifts$ | async)?.length ?? 0)"
            [description]="'Upcoming shifts today'"
            [shifts]="(upcomingShifts$ | async) ?? []"
          ></app-upcoming-shifts-widget>
          <app-number-widget
            *ngSwitchCase="'notifications'"
            [title]="'Notifications'"
            [value]="(notifications$ | async) ?? 0"
            [description]="'Unread notifications'"
            color="accent"
          ></app-number-widget>
          <app-number-widget
            *ngSwitchCase="'messages'"
            [title]="'Messages'"
            [value]="(messages$ | async) ?? 0"
            [description]="'Unread messages'"
            color="primary"
          ></app-number-widget>
          <div *ngSwitchDefault class="widget-placeholder">
            {{ widget.title }}
          </div>
        </ng-container>
      </mat-grid-tile>
    </mat-grid-list>
  `,
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnDestroy {
  widgets: DashboardWidget[] = [];
  editMode = false;
  private sub = new Subscription();

  staffOnShift$: Observable<number>;
  upcomingShifts$: Observable<any[]>;
  notifications$: Observable<number>;
  messages$: Observable<number>;

  constructor(
    private dialog: MatDialog,
    private dashboardState: DashboardStateService,
    private widgetData: WidgetDataService
  ) {
    this.sub.add(this.dashboardState.widgets$.subscribe(w => this.widgets = w));
    this.sub.add(this.dashboardState.editMode$.subscribe(e => this.editMode = e));
    this.staffOnShift$ = this.widgetData.getStaffOnShift();
    this.upcomingShifts$ = this.widgetData.getUpcomingShifts();
    this.notifications$ = this.widgetData.getNotifications();
    this.messages$ = this.widgetData.getMessages();
  }

  toggleEditMode() {
    this.dashboardState.setEditMode(!this.editMode);
  }

  openWidgetSettings(widget: DashboardWidget) {
    // Dynamically import the dialog only when needed
    import('./widget-settings-dialog.component').then(({ WidgetSettingsDialogComponent }) => {
      const dialogRef = this.dialog.open(WidgetSettingsDialogComponent, {
        data: { settings: widget.settings || {} }
      });
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.dashboardState.updateWidgetSettings(widget.id, result);
        }
      });
    });
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }
} 