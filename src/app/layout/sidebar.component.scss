@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

// Sidebar Header Section (contains hamburger toggle)
.sidebar-header {
  flex-shrink: 0;
  padding: 12px 8px 8px 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.95);

  // Collapsed state
  .sidebar-nav.collapsed & {
    padding: 8px 4px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    max-width: 64px !important;
    overflow: hidden !important;
  }
}

// Hamburger Toggle Button Styles
.sidebar-toggle-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  // Center in collapsed state
  .sidebar-nav.collapsed & {
    justify-content: center !important;
  }
}

.hamburger-toggle {
  width: 44px !important;
  height: 44px !important;
  min-width: 44px !important;
  padding: 0 !important;
  border-radius: 6px !important;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  z-index: 100 !important;

  // Default state (expanded) - blue background with white lines
  background: #1976d2 !important;
  border: none !important;

  &:hover {
    background: #1565c0 !important;
  }

  &:focus {
    outline: 2px solid #42a5f5;
    outline-offset: 2px;
    background: #1565c0 !important;
  }

  // Collapsed state - transparent background with blue lines
  .sidebar-nav.collapsed & {
    background: transparent !important;
    border: none !important;

    &:hover {
      background: rgba(25, 118, 210, 0.04) !important;
    }

    &:focus {
      background: rgba(25, 118, 210, 0.08) !important;
    }
  }
}

.hamburger-icon {
  width: 20px;
  height: 16px;
  position: relative;
  display: flex !important;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
}

.hamburger-line {
  width: 100% !important;
  height: 3px !important;
  border-radius: 1px !important;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important;
  transform-origin: center !important;
  display: block !important;
  margin: 0 !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 999 !important;

  // CRITICAL FIX: Default state (expanded) - white lines on blue background
  background-color: #ffffff !important;
  background: #ffffff !important;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1) !important;

  .hamburger-toggle:hover & {
    background-color: rgba(255, 255, 255, 0.95) !important;
    background: rgba(255, 255, 255, 0.95) !important;
  }

  // CRITICAL FIX: Collapsed state - blue lines on transparent background
  .sidebar-nav.collapsed .hamburger-toggle & {
    background-color: #1976d2 !important;
    background: #1976d2 !important;
    height: 3px !important;
    visibility: visible !important;
    opacity: 1 !important;
    box-shadow: 0 0 2px rgba(25, 118, 210, 0.3) !important;
  }

  .sidebar-nav.collapsed .hamburger-toggle:hover & {
    background-color: #1565c0 !important;
    background: #1565c0 !important;
    height: 3px !important;
    box-shadow: 0 0 2px rgba(21, 101, 192, 0.4) !important;
  }
}

// Hamburger icon remains static - no animation transformations
.hamburger-icon {
  .hamburger-line {
    // Keep all lines as simple horizontal bars
    transform: none;
    width: 100%;
    opacity: 1;
  }
}

// CRITICAL FIX: Dark theme support for hamburger menu and entire sidebar
.dark-theme {
  .hamburger-line {
    // CRITICAL FIX: Default state (expanded) - white lines on blue background in dark mode
    background-color: #ffffff !important;
    background: #ffffff !important;
    visibility: visible !important;
    opacity: 1 !important;
    box-shadow: 0 0 1px rgba(255, 255, 255, 0.2) !important;

    .hamburger-toggle:hover & {
      background-color: rgba(255, 255, 255, 0.95) !important;
      background: rgba(255, 255, 255, 0.95) !important;
    }

    // CRITICAL FIX: Collapsed state - light blue lines on transparent background in dark mode
    .sidebar-nav.collapsed .hamburger-toggle & {
      background-color: #42a5f5 !important;
      background: #42a5f5 !important;
      height: 3px !important;
      visibility: visible !important;
      opacity: 1 !important;
      box-shadow: 0 0 2px rgba(66, 165, 245, 0.4) !important;
    }

    .sidebar-nav.collapsed .hamburger-toggle:hover & {
      background-color: #64b5f6 !important;
      background: #64b5f6 !important;
      height: 3px !important;
      box-shadow: 0 0 2px rgba(100, 181, 246, 0.5) !important;
    }
  }

  .hamburger-toggle {
    // Default state (expanded) - blue background with white lines in dark mode
    background: #1976d2 !important;
    border: none !important;

    &:hover {
      background: #1565c0 !important;
    }

    &:focus {
      outline: 2px solid #42a5f5;
      outline-offset: 2px;
      background: #1565c0 !important;
    }

    // Collapsed state - transparent background with light blue lines in dark mode
    .sidebar-nav.collapsed & {
      background: transparent !important;
      border: none !important;

      &:hover {
        background: rgba(66, 165, 245, 0.08) !important;
      }

      &:focus {
        background: rgba(66, 165, 245, 0.12) !important;
      }
    }
  }

  // Dark theme sidebar background and elements
  .sidebar-nav {
    background: linear-gradient(180deg, #1e1e1e 0%, #121212 100%) !important;

    &::before {
      background: linear-gradient(180deg, #42a5f5 0%, #1976d2 100%) !important;
    }
  }

  .sidebar-header {
    background: rgba(30, 30, 30, 0.95) !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  .sidebar-business-section {
    background: rgba(30, 30, 30, 0.95) !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  .sidebar-section-label {
    color: #42a5f5 !important;
    text-shadow: 0 1px 2px rgba(66, 165, 245, 0.2) !important;
  }

  .mat-list-item {
    color: rgba(255, 255, 255, 0.87) !important;

    &:hover, &:focus {
      background: rgba(66, 165, 245, 0.1) !important;
      color: #42a5f5 !important;
    }

    &.active {
      background: #42a5f5 !important;
      color: #000 !important;

      .mat-icon {
        color: #000 !important;
      }
    }
  }

  .mat-icon {
    color: #42a5f5 !important; // CRITICAL FIX: Light blue icons in dark mode
    opacity: 1 !important;
    visibility: visible !important;
  }

  .sidebar-divider {
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  // Dark theme scrollbar
  .mat-nav-list {
    &::-webkit-scrollbar-thumb {
      background: rgba(66, 165, 245, 0.3) !important;

      &:hover {
        background: rgba(66, 165, 245, 0.5) !important;
      }
    }
  }

  // Dark theme notification badges
  .mat-badge-content {
    border-color: #1e1e1e !important;
  }
}

// Mobile responsive behavior
@media (max-width: 900px) {
  .hamburger-toggle {
    &.collapsed {
      position: absolute; // Reset to relative positioning on mobile
      top: 12px;
      left: auto;
      right: 12px;
    }
  }
}

// CRITICAL: Host-level collapsed state with maximum specificity
:host(.collapsed) nav.sidebar-nav,
:host nav.sidebar-nav.collapsed {
  width: 64px !important;
  max-width: 64px !important;
  min-width: 64px !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  flex-basis: 64px !important;
  flex-grow: 0 !important;
  flex-shrink: 0 !important;
}

.mat-nav-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 0 100px 0; // Bottom padding for UserMenu space
  min-height: 0; // Allow flex item to shrink

  // Custom scrollbar for navigation
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(25, 118, 210, 0.5);
    }
  }

  // Collapsed state constraints
  .sidebar-nav.collapsed & {
    padding: 8px 0 100px 0 !important;
    overflow-x: hidden !important;
  }
}

.mat-list-item.active {
  background: #e3eaf2;
  color: #1976d2;
  font-weight: 600;
}

.mat-list-item {
  border-radius: 6px;
  margin-bottom: 4px;
  transition: background 0.2s;
}

.mat-list-item:hover {
  background: #f0f4f8;
}

.mat-icon {
  margin-right: 16px;
}

.sidebar-nav {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden; // Prevent sidebar from scrolling
  padding: 0;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important; // CRITICAL FIX: Smooth animations

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #1976d2 0%, #42a5f5 100%);
    z-index: 1;
    transition: opacity 0.3s ease !important;
  }

  // CRITICAL FIX: Collapsed state - Exactly 64px width with smooth transitions
  &.collapsed {
    width: 64px !important;
    max-width: 64px !important;
    min-width: 64px !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    box-sizing: border-box !important;
    flex-basis: 64px !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;

    &::before {
      opacity: 0.7 !important; // Subtle accent line in collapsed mode
    }
  }
}

// Business Selector Section
.sidebar-business-section {
  flex-shrink: 0; // Prevent compression
  padding: 16px 8px;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);

  // Collapsed state
  .sidebar-nav.collapsed & {
    padding: 8px 4px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    max-width: 64px !important;
    overflow: hidden !important;
  }
}

.sidebar-divider {
  margin: 8px 0;

  // Hide dividers in collapsed state
  .sidebar-nav.collapsed & {
    display: none;
  }
}

.sidebar-section-label {
  font-size: 1rem;
  font-weight: 700;
  color: #1976d2;
  letter-spacing: 0.04em;
  margin: 16px 0 6px 24px;
  text-shadow: 0 1px 2px rgba(25, 118, 210, 0.08);
}

.mat-nav-list {
  padding-top: 0;
}

.mat-list-item {
  border-radius: 8px;
  margin: 2px 8px;
  transition: background 0.18s, color 0.18s;
  color: #222;
  font-weight: 500;
  min-height: 48px; // Touch-friendly minimum 44px + padding
  padding: 8px 12px; // Ensure adequate touch target

  &:hover, &:focus {
    background: #e3eaf2;
    color: var(--mui-palette-primary-main, #1976d2);
  }
  &.active {
    background: #1976d2;
    color: #fff;
    font-weight: 700;
    .mat-icon {
      color: #fff;
    }
  }
}

.mat-icon {
  margin-right: 18px;
  color: #1976d2 !important; // CRITICAL FIX: Force icon color visibility
  font-variation-settings: 'wght' 400;
  font-size: 1.6rem !important; // Default size: ~25.6px
  transition: color 0.18s;
  flex-shrink: 0; // ISSUE 2: Prevent icon from shrinking and affecting text layout
  opacity: 1 !important; // CRITICAL FIX: Ensure icons are fully visible
  visibility: visible !important;
}

// ISSUE 2: Ensure proper text layout in expanded mode
.sidebar-nav:not(.collapsed) {
  .mat-list-item {
    display: flex !important;
    align-items: center !important;

    .mat-list-item-content {
      display: flex !important;
      align-items: center !important;
      width: 100% !important;
      overflow: hidden !important;
    }

    span:not(.mat-icon) {
      flex: 1 !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      margin-left: 0 !important;
    }
  }
}

.sidebar-business-switcher {
  padding: 0 16px 8px 16px;
  .business-switcher-field {
    width: 100%;
    margin: 0;
    ::ng-deep .mat-form-field-outline {
      border-radius: 8px;
    }
    ::ng-deep .mat-select-value {
      font-weight: 600;
      color: #1976d2;
    }
  }
}

// Collapsed state styles - CRITICAL: Perfect 64px width with centered icons
.sidebar-nav.collapsed {
  // Hide text elements and labels
  .sidebar-section-label,
  .sidebar-expand-icon,
  .sidebar-submenu {
    display: none !important;
  }

  // Hide all text spans but keep icons
  span:not(.mat-icon):not([class*="mat-icon"]) {
    display: none !important;
  }

  // Hide text content in mat-list-item
  .mat-list-item-content span:not(.mat-icon) {
    display: none !important;
  }

  // Center icons and adjust spacing - CRITICAL: Perfect centering with touch-friendly targets
  .mat-list-item {
    justify-content: center !important;
    padding: 8px 4px !important;
    margin: 2px 4px !important;
    min-height: 48px !important; // Touch-friendly targets
    width: calc(100% - 8px) !important;
    max-width: 56px !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;

    .mat-list-item-content {
      justify-content: center !important;
      padding: 0 !important;
      overflow: hidden !important;
      width: 100% !important;
      max-width: 48px !important;
      display: flex !important;
      align-items: center !important;
      flex-direction: row !important;
    }

    .mat-icon {
      margin-right: 0 !important;
      margin-left: 0 !important;
      font-size: 48px !important; // 48px for touch-friendly targets
      width: 48px !important;
      height: 48px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }

  // Show notification badges in collapsed state - positioned on icons
  .mat-badge,
  [matBadge] {
    position: relative !important;

    .mat-badge-content {
      display: block !important;
      position: absolute !important;
      top: -8px !important;
      right: -8px !important;
      background: #f44336 !important;
      color: white !important;
      border-radius: 50% !important;
      min-width: 16px !important;
      height: 16px !important;
      font-size: 10px !important;
      font-weight: 600 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      border: 2px solid var(--sidebar-bg, #f8fafc) !important;
      z-index: 10 !important;
    }
  }

  // Ensure navigation list is properly constrained
  .mat-nav-list {
    width: 100% !important;
    max-width: 72px !important;
    overflow: hidden !important;
    padding: 8px 0 100px 0 !important;
  }

  // Constrain all child elements
  * {
    max-width: 72px !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  // Ensure no element can expand beyond 72px
  > * {
    max-width: 72px !important;
    box-sizing: border-box !important;
  }

  // Specifically constrain UserIcon in collapsed state
  app-user-icon {
    max-width: 72px !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  // Constrain business selector in collapsed state
  app-business-selector {
    max-width: 72px !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }
}

// MAXIMUM SPECIFICITY for collapsed icon sizing - TOUCH FRIENDLY
:host(.collapsed) .sidebar-nav .mat-nav-list .mat-list-item .mat-icon,
:host .sidebar-nav.collapsed .mat-nav-list .mat-list-item .mat-icon,
.sidebar-nav.collapsed .mat-nav-list .mat-list-item .mat-icon,
app-sidebar .sidebar-nav.collapsed .mat-list-item .mat-icon {
  font-size: 3rem !important; // 48px - TOUCH FRIENDLY
  width: 48px !important;
  height: 48px !important;
  margin: 0 !important;
  line-height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.sidebar-toggle-btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 8px 0 0;
}

.sidebar-collapsible {
  .sidebar-expand-icon {
    margin-left: auto;
    transition: transform 0.18s;
  }
  .sidebar-submenu {
    padding-left: 32px;
    .sidebar-subitem {
      font-size: 0.97rem;
      .mat-icon {
        font-size: 1.2rem;
        margin-right: 12px;
      }
    }
  }
}

// ISSUE 6: Comprehensive tooltip styling for ALL sidebar elements
::ng-deep .mat-mdc-tooltip {
  background: #666666 !important; // Grey background
  color: #ffffff !important; // White text
  font-size: 0.875rem !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 1050 !important; // Proper layering below header
  max-width: 200px !important;
  word-wrap: break-word !important;
  margin-left: 12px !important; // Position to the right of collapsed sidebar
  margin-top: 8px !important; // Prevent overlap with header
}

// Specific tooltip positioning for collapsed sidebar elements
::ng-deep .sidebar-nav.collapsed {
  // Hamburger menu tooltip
  .hamburger-toggle .mat-mdc-tooltip {
    margin-left: 16px !important;
    margin-top: 12px !important; // Extra spacing to avoid header overlap
  }

  // Navigation icons tooltips
  .mat-list-item .mat-mdc-tooltip {
    margin-left: 16px !important;
    margin-top: 0 !important;
  }

  // Business selector tooltip
  app-business-selector .mat-mdc-tooltip {
    margin-left: 16px !important;
    margin-top: 0 !important;
  }

  // User menu tooltip
  app-user-icon .mat-mdc-tooltip {
    margin-left: 16px !important;
    margin-top: 0 !important;
  }
}

// Ensure tooltips don't interfere with dashboard widgets
::ng-deep .mat-mdc-tooltip-panel {
  z-index: 1050 !important; // Below header (1100) but above content
}

// MAXIMUM SPECIFICITY - ENSURE HAMBURGER VISIBILITY WITH TRANSPARENT BACKGROUND
:host ::ng-deep .hamburger-toggle,
:host .hamburger-toggle,
.sidebar-nav .hamburger-toggle,
app-sidebar .hamburger-toggle {
  background: transparent !important; // ISSUE 1: Transparent background
  border: none !important;
  width: 44px !important;
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 100 !important;

  &:hover {
    background: rgba(25, 118, 210, 0.04) !important;
  }

  &:focus {
    background: rgba(25, 118, 210, 0.08) !important;
  }
}

// Removed problematic hamburger line override - styles are now properly handled above

// Responsive design - Mobile layout handled by parent
@media (max-width: 900px) {
  .sidebar-nav {
    // Mobile layout: always show full content
    .mobile-layout & {
      .sidebar-section-label,
      .sidebar-business-switcher,
      .sidebar-divider,
      span:not(.mat-icon),
      .sidebar-expand-icon {
        display: block !important;
      }
    }
  }
}