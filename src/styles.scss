/* You can add global styles to this file, and also import other style files */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* StaffManager Global Styles */
:root {
  /* Light Theme Variables */
  --sm-primary-main: #1976d2;
  --sm-primary-light: #42a5f5;
  --sm-primary-dark: #1565c0;
  --sm-secondary-main: #9c27b0;
  --sm-accent-main: #ff6b35;
  --sm-background-default: #f7f9fa;
  --sm-background-paper: #ffffff;
  --sm-text-primary: #1a1a1a;
  --sm-text-secondary: #666666;
  --sm-spacing-xs: 4px;
  --sm-spacing-sm: 8px;
  --sm-spacing-md: 16px;
  --sm-spacing-lg: 24px;
  --sm-spacing-xl: 32px;
  --sm-border-radius-small: 4px;
  --sm-border-radius-medium: 8px;
  --sm-border-radius-large: 12px;
  --sm-shadow-light: 0 2px 4px rgba(0,0,0,0.1);
  --sm-shadow-medium: 0 4px 8px rgba(0,0,0,0.12);
  --sm-shadow-heavy: 0 8px 16px rgba(0,0,0,0.15);
}

/* Dark Theme Variables */
.dark-theme {
  --sm-primary-main: #42a5f5;
  --sm-primary-light: #64b5f6;
  --sm-primary-dark: #1976d2;
  --sm-secondary-main: #ba68c8;
  --sm-accent-main: #ff8a65;
  --sm-background-default: #121212;
  --sm-background-paper: #1e1e1e;
  --sm-text-primary: #ffffff;
  --sm-text-secondary: #b3b3b3;
  --sm-shadow-light: 0 2px 4px rgba(0,0,0,0.3);
  --sm-shadow-medium: 0 4px 8px rgba(0,0,0,0.4);
  --sm-shadow-heavy: 0 8px 16px rgba(0,0,0,0.5);
}

html, body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, "Helvetica Neue", sans-serif;
  background: var(--sm-background-default);
  color: var(--sm-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* CRITICAL FIX: FORCE HAMBURGER LINES TO BE VISIBLE */
.hamburger-line,
app-sidebar .hamburger-line,
.sidebar-nav .hamburger-line {
  background-color: #ffffff !important;
  background: #ffffff !important;
  width: 100% !important;
  height: 3px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 999 !important;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1) !important;
}

.sidebar-nav.collapsed .hamburger-line,
.sidebar-nav.collapsed app-sidebar .hamburger-line {
  background-color: #1976d2 !important;
  background: #1976d2 !important;
  box-shadow: 0 0 2px rgba(25, 118, 210, 0.3) !important;
}

.dark-theme .hamburger-line {
  background-color: #ffffff !important;
  background: #ffffff !important;
  box-shadow: 0 0 1px rgba(255, 255, 255, 0.2) !important;
}

.dark-theme .sidebar-nav.collapsed .hamburger-line {
  background-color: #42a5f5 !important;
  background: #42a5f5 !important;
  box-shadow: 0 0 2px rgba(66, 165, 245, 0.4) !important;
}

/* CRITICAL FIX: FORCE MATERIAL ICONS TO BE VISIBLE */
.mat-icon,
mat-icon,
.material-icons {
  color: #1976d2 !important;
  opacity: 1 !important;
  visibility: visible !important;
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  display: inline-block !important;
  line-height: 1 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-wrap: normal !important;
  white-space: nowrap !important;
  direction: ltr !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

/* Dark theme icon colors */
.dark-theme .mat-icon,
.dark-theme mat-icon,
.dark-theme .material-icons {
  color: #42a5f5 !important;
}

/* Global Material Design Overrides */
.mat-mdc-card {
  background: var(--sm-background-paper) !important;
  color: var(--sm-text-primary) !important;
}

.mat-mdc-button {
  font-family: 'Inter', sans-serif !important;
}

.mat-mdc-form-field {
  font-family: 'Inter', sans-serif !important;
}

/* Utility Classes */
.text-primary { color: var(--sm-text-primary) !important; }
.text-secondary { color: var(--sm-text-secondary) !important; }
.bg-primary { background-color: var(--sm-primary-main) !important; }
.bg-secondary { background-color: var(--sm-secondary-main) !important; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--sm-background-default);
}

::-webkit-scrollbar-thumb {
  background: var(--sm-text-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--sm-text-primary);
}