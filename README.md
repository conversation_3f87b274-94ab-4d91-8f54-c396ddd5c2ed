# StaffManager - Advanced Staff Management System

[![Angular](https://img.shields.io/badge/Angular-19.2.13-red.svg)](https://angular.io/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Firebase](https://img.shields.io/badge/Firebase-10.0+-orange.svg)](https://firebase.google.com/)
[![Material Design](https://img.shields.io/badge/Material%20Design-17.0+-green.svg)](https://material.angular.io/)

StaffManager is a comprehensive, modern staff management application built with Angular 19, featuring advanced scheduling, time management, business profile management, and AI-powered insights. Designed for businesses of all sizes to efficiently manage their workforce.

**⚠️ Current Status: IN DEVELOPMENT - NOT PRODUCTION READY**
- UI foundation and basic structure completed
- Authentication system has critical issues (user always null)
- Most features use mock data or placeholder implementations
- Requires significant development work before production
- Estimated 2-4 weeks additional development needed

## 🚀 Features

### Core Modules
- **📊 Enhanced Dashboard** - Customizable widgets with drag-drop functionality
- **👥 Staff Management** - Complete staff profiles with availability tracking
- **📅 Calendar Integration** - FullCalendar with Firestore sync and staff assignment
- **⏰ Time Management** - Comprehensive time tracking, scheduling, and time-off management
- **🏢 Business Profiles** - Multi-business support with Hours of Operation (HOO) and Hours of Business (HOB)
- **⚙️ Advanced Settings** - Complete customization and system monitoring

### Advanced Features
- **🤖 AI Integration** - Gemini 2.5 Flash for intelligent scheduling and insights
- **🎨 Custom Theming** - Light/dark modes with full appearance customization
- **📱 PWA Support** - Progressive Web App with offline capabilities
- **🔐 Firebase Authentication** - Secure user management with role-based access
- **📊 Real-time Data** - Live updates with Firestore integration
- **🌐 Responsive Design** - Mobile-first approach with Material Design
- **⚡ Optimized Performance** - Firebase injection context fixes for stability

## 🛠️ Technology Stack

- **Frontend**: Angular 19 with standalone components
- **UI Framework**: Angular Material 17+
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **AI Services**: Google Gemini 2.5 Flash Preview
- **Calendar**: FullCalendar integration
- **State Management**: RxJS with reactive patterns
- **Styling**: SCSS with Material Design theming
- **Build Tool**: Angular CLI with Vite

## 📋 Prerequisites

- Node.js 18+ and npm
- Angular CLI 19+
- Firebase project with Firestore and Authentication enabled
- Google AI API key for Gemini integration (optional)

## 🚀 Quick Start

### 1. Clone and Install
```bash
git clone <repository-url>
cd staffmanager-web-new
npm install
```

### 2. Firebase Configuration
Create `src/environments/environment.ts`:
```typescript
export const environment = {
  production: false,
  firebase: {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
  },
  geminiApiKey: "your-gemini-api-key" // Optional
};
```

### 3. Development Server
```bash
npm start
```
The app will automatically find an available port and open in your browser.

### 4. Build for Production
```bash
npm run build
```

## 📁 Project Structure

```
src/
├── app/
│   ├── core/                    # Core services and models
│   │   ├── auth/               # Authentication services
│   │   ├── models/             # Data models and interfaces
│   │   ├── services/           # Business logic services
│   │   └── theme/              # Theme management
│   ├── features/               # Feature modules
│   │   ├── auth/              # Authentication components
│   │   ├── business-profile/   # Business management
│   │   ├── calendar/          # Calendar functionality
│   │   ├── dashboard/         # Dashboard and widgets
│   │   ├── settings/          # Settings management
│   │   ├── staff/             # Staff management
│   │   └── time-management/   # Time tracking and scheduling
│   ├── layout/                # Layout components
│   │   ├── header/            # Application header
│   │   └── sidebar/           # Navigation sidebar
│   └── shared/                # Shared components and utilities
├── assets/                    # Static assets
├── environments/              # Environment configurations
└── styles/                   # Global styles and themes
```

## 🎯 Key Features Documentation

### Dashboard System
- **Customizable Widgets**: Drag-drop interface with resizable widgets
- **Real-time Data**: Live updates from Firestore
- **Widget Builder**: Create custom widgets with various data sources
- **Responsive Layout**: Adaptive grid system for all screen sizes

### Staff Management
- **Complete Profiles**: Personal info, contact details, availability
- **Role Management**: Manager, staff, and admin roles
- **Availability Tracking**: Weekly schedules with time slots
- **Integration**: Seamless connection with calendar and time management

### Time Management Module
- **Staff Availability**: Visual availability management with AI insights
- **Time Off Requests**: Complete workflow with approval system
- **Scheduling Interface**: Manual and AI-powered scheduling
- **Time Tracking**: Clock in/out with various methods
- **Reports**: Comprehensive time and attendance reporting

### Business Profile System
- **Multi-business Support**: Manage multiple business locations
- **Hours of Operation (HOO)**: Customer-facing business hours
- **Hours of Business (HOB)**: Internal operational hours
- **Operational Settings**: Capacity limits, break policies, overtime rules
- **Staff Settings**: Scheduling preferences, time tracking methods

### Settings System
- **User Profile**: Personal account and staff profile management
- **Appearance**: Complete theme customization with real-time preview
- **Systems Check**: Real-time monitoring of AI, database, and PWA services
- **Notifications**: Email, push, and in-app notification preferences
- **Security**: Password management and privacy controls
- **Integrations**: Third-party service connections

## 🔧 Development

### Code Standards
- **TypeScript**: Strict mode enabled with comprehensive typing
- **Angular Style Guide**: Following official Angular conventions
- **Material Design**: Consistent UI/UX patterns
- **Reactive Programming**: RxJS for state management
- **Standalone Components**: Modern Angular architecture

### Testing
```bash
# Unit tests
npm test

# E2E tests
npm run e2e

# Coverage report
npm run test:coverage
```

### Linting and Formatting
```bash
# Lint code
npm run lint

# Format code
npm run format
```

## 🚀 Deployment

### Firebase Hosting
```bash
npm run build
firebase deploy
```

### Docker
```bash
docker build -t staffmanager .
docker run -p 80:80 staffmanager
```

## 📖 Documentation

- [**Architecture Guide**](docs/ARCHITECTURE.md) - System architecture and design patterns
- [**API Documentation**](docs/API.md) - Service interfaces and data models
- [**Component Guide**](docs/COMPONENTS.md) - Component documentation and usage
- [**User Profile Access Guide**](docs/USER_PROFILE_ACCESS_GUIDE.md) - How to access your profile correctly
- [**Deployment Guide**](docs/DEPLOYMENT.md) - Production deployment instructions
- [**Firebase Injection Context Fix**](docs/FIREBASE_INJECTION_CONTEXT_FIX.md) - Firebase stability improvements
- [**Contributing Guide**](docs/CONTRIBUTING.md) - Development guidelines and standards

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs](docs/) folder for detailed guides
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions for questions and ideas

## 🎯 Roadmap

- [ ] Mobile app development (React Native/Flutter)
- [ ] Advanced AI features and predictive analytics
- [ ] Integration with popular payroll systems
- [ ] Multi-language support
- [ ] Advanced reporting and analytics dashboard
- [ ] Voice commands and accessibility improvements

---

**Built with ❤️ using Angular 19 and Material Design**
