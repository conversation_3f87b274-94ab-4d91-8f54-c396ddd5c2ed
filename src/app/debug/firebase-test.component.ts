import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FirebaseContextService } from '../core/services/firebase-context.service';
import { AuthService } from '../core/auth/auth.service';

/**
 * Firebase Test Component
 * 
 * Simple component to test Firebase operations and debug issues
 */
@Component({
  selector: 'app-firebase-test',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule
  ],
  template: `
    <div style="padding: 24px; max-width: 600px; margin: 0 auto;">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Firebase Test</mat-card-title>
          <mat-card-subtitle>Test Firebase operations</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div style="display: flex; flex-direction: column; gap: 16px; margin-top: 16px;">
            <button mat-raised-button color="primary" (click)="testFirebaseWrite()">
              Test Firebase Write
            </button>
            
            <button mat-raised-button color="accent" (click)="testFirebaseRead()">
              Test Firebase Read
            </button>
            
            <button mat-raised-button (click)="testUserProfile()">
              Test User Profile
            </button>
            
            <button mat-raised-button color="warn" (click)="clearTestData()">
              Clear Test Data
            </button>
          </div>
          
          <div *ngIf="lastResult" style="margin-top: 24px; padding: 16px; background: #f5f5f5; border-radius: 4px;">
            <h4>Last Result:</h4>
            <pre>{{ lastResult }}</pre>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `
})
export class FirebaseTestComponent {
  private firebaseContext = inject(FirebaseContextService);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);

  lastResult = '';

  testFirebaseWrite(): void {
    console.log('🧪 Testing Firebase write...');
    
    const testData = {
      message: 'Hello Firebase!',
      timestamp: new Date(),
      testId: Date.now().toString()
    };

    this.firebaseContext.setDocument('test/write-test', testData).subscribe({
      next: () => {
        console.log('✅ Firebase write successful');
        this.lastResult = 'Write successful: ' + JSON.stringify(testData, null, 2);
        this.snackBar.open('Firebase write successful!', 'Close', { duration: 3000 });
      },
      error: (error) => {
        console.error('❌ Firebase write failed:', error);
        this.lastResult = 'Write failed: ' + error.message;
        this.snackBar.open('Firebase write failed: ' + error.message, 'Close', { duration: 5000 });
      }
    });
  }

  testFirebaseRead(): void {
    console.log('🧪 Testing Firebase read...');
    
    this.firebaseContext.getDocument('test/write-test').subscribe({
      next: (docSnap) => {
        if (docSnap?.exists()) {
          const data = docSnap.data();
          console.log('✅ Firebase read successful:', data);
          this.lastResult = 'Read successful: ' + JSON.stringify(data, null, 2);
          this.snackBar.open('Firebase read successful!', 'Close', { duration: 3000 });
        } else {
          console.log('📄 Document does not exist');
          this.lastResult = 'Document does not exist';
          this.snackBar.open('Document does not exist', 'Close', { duration: 3000 });
        }
      },
      error: (error) => {
        console.error('❌ Firebase read failed:', error);
        this.lastResult = 'Read failed: ' + error.message;
        this.snackBar.open('Firebase read failed: ' + error.message, 'Close', { duration: 5000 });
      }
    });
  }

  testUserProfile(): void {
    console.log('🧪 Testing user profile...');
    
    this.authService.userProfile$.subscribe({
      next: (profile) => {
        if (profile) {
          console.log('✅ User profile loaded:', profile);
          this.lastResult = 'User profile: ' + JSON.stringify(profile, null, 2);
          this.snackBar.open('User profile loaded successfully!', 'Close', { duration: 3000 });
        } else {
          console.log('👤 No user profile found');
          this.lastResult = 'No user profile found';
          this.snackBar.open('No user profile found', 'Close', { duration: 3000 });
        }
      },
      error: (error) => {
        console.error('❌ User profile failed:', error);
        this.lastResult = 'User profile failed: ' + error.message;
        this.snackBar.open('User profile failed: ' + error.message, 'Close', { duration: 5000 });
      }
    });
  }

  clearTestData(): void {
    console.log('🧹 Clearing test data...');
    
    this.firebaseContext.deleteDocument('test/write-test').subscribe({
      next: () => {
        console.log('✅ Test data cleared');
        this.lastResult = 'Test data cleared successfully';
        this.snackBar.open('Test data cleared!', 'Close', { duration: 3000 });
      },
      error: (error) => {
        console.error('❌ Clear test data failed:', error);
        this.lastResult = 'Clear failed: ' + error.message;
        this.snackBar.open('Clear failed: ' + error.message, 'Close', { duration: 5000 });
      }
    });
  }
}
