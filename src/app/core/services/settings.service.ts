import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, combineLatest } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import {
  Firestore,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  Timestamp
} from '@angular/fire/firestore';
import { FirebaseContextService } from './firebase-context.service';

import {
  UserSettings,
  SystemStatus,
  ServiceStatus,
  DEFAULT_USER_SETTINGS,
  SettingsCategory
} from '../models/settings.model';
import { AuthService } from '../auth/auth.service';
import { StaffManagerThemeService } from '../theme/staffmanager-theme';

@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  private firestore = inject(Firestore);
  private authService = inject(AuthService);
  private themeService = inject(StaffManagerThemeService);
  private firebaseContext = inject(FirebaseContextService);

  private userSettingsSubject = new BehaviorSubject<UserSettings | null>(null);
  public userSettings$ = this.userSettingsSubject.asObservable();

  private systemStatusSubject = new BehaviorSubject<SystemStatus | null>(null);
  public systemStatus$ = this.systemStatusSubject.asObservable();

  constructor() {
    // Load user settings when authenticated
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.uid) {
        this.loadUserSettings(profile.uid);
      }
    });

    // Perform initial system check
    this.performSystemCheck();

    // Schedule periodic system checks (every 5 minutes)
    setInterval(() => this.performSystemCheck(), 5 * 60 * 1000);
  }

  /**
   * Load user settings from Firestore
   */
  async loadUserSettings(userId: string): Promise<void> {
    try {
      // Use FirebaseContextService for proper injection context
      this.firebaseContext.subscribeToDocument(`userSettings/${userId}`).subscribe({
        next: (docSnap) => {
          if (docSnap.exists()) {
            const data = docSnap.data();
            const settings = this.convertFirestoreToSettings(data);
            this.userSettingsSubject.next(settings);
            this.applySettings(settings);
          } else {
            // Create default settings if none exist
            this.createDefaultSettings(userId);
          }
        },
        error: (error) => {
          console.error('Error loading user settings:', error);
          // Fallback to default settings
          this.createDefaultSettings(userId);
        }
      });
    } catch (error) {
      console.error('Error setting up user settings subscription:', error);
      // Fallback to default settings
      this.createDefaultSettings(userId);
    }
  }

  /**
   * Create default settings for new user
   */
  private async createDefaultSettings(userId: string): Promise<void> {
    const defaultSettings: UserSettings = {
      userId,
      ...DEFAULT_USER_SETTINGS,
      createdAt: new Date(),
      updatedAt: new Date(),
      version: '1.0.0'
    } as UserSettings;

    await this.saveUserSettings(defaultSettings);
  }

  /**
   * Save user settings to Firestore
   */
  async saveUserSettings(settings: UserSettings): Promise<void> {
    try {
      const docRef = doc(this.firestore, 'userSettings', settings.userId);
      const firestoreData = this.convertSettingsToFirestore(settings);
      await setDoc(docRef, firestoreData, { merge: true });
    } catch (error) {
      console.error('Error saving user settings:', error);
      throw error;
    }
  }

  /**
   * Update specific settings section
   */
  async updateSettings(userId: string, updates: Partial<UserSettings>): Promise<void> {
    try {
      const docRef = doc(this.firestore, 'userSettings', userId);
      const firestoreUpdates = this.convertSettingsToFirestore(updates as UserSettings);
      await updateDoc(docRef, {
        ...firestoreUpdates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  }

  /**
   * Apply settings to the application
   */
  private applySettings(settings: UserSettings): void {
    // Apply theme settings
    if (settings.appearance) {
      this.applyThemeSettings(settings.appearance);
    }

    // Apply other settings as needed
    this.applyLayoutSettings(settings.appearance?.layout);
    this.applyAccessibilitySettings(settings.appearance?.accessibility);
  }

  /**
   * Apply theme settings
   */
  private applyThemeSettings(appearance: any): void {
    if (appearance.theme) {
      switch (appearance.theme) {
        case 'light':
          this.themeService.setTheme(false);
          break;
        case 'dark':
          this.themeService.setTheme(true);
          break;
        case 'system':
          // Use system preference
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          this.themeService.setTheme(prefersDark);
          break;
      }
    }

    // Apply custom colors if available (theme service may not support this yet)
    // if (appearance.colorScheme) {
    //   this.themeService.updateColorScheme(appearance.colorScheme);
    // }
  }

  /**
   * Apply layout settings
   */
  private applyLayoutSettings(layout: any): void {
    if (!layout) return;

    // Apply layout settings to document root
    const root = document.documentElement;

    if (layout.density) {
      root.setAttribute('data-density', layout.density);
    }

    if (layout.widgetSpacing) {
      root.setAttribute('data-widget-spacing', layout.widgetSpacing);
    }
  }

  /**
   * Apply accessibility settings
   */
  private applyAccessibilitySettings(accessibility: any): void {
    if (!accessibility) return;

    const root = document.documentElement;

    if (accessibility.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    if (accessibility.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }

    if (accessibility.focusIndicators) {
      root.classList.add('enhanced-focus');
    } else {
      root.classList.remove('enhanced-focus');
    }
  }

  /**
   * Perform comprehensive system check
   */
  async performSystemCheck(): Promise<void> {
    const systemStatus: SystemStatus = {
      overall: 'healthy',
      services: {
        ai: await this.checkAIService(),
        database: await this.checkDatabaseService(),
        pwa: await this.checkPWAService(),
        auth: await this.checkAuthService(),
        storage: await this.checkStorageService(),
        notifications: await this.checkNotificationService()
      },
      lastChecked: new Date()
    };

    // Determine overall status
    const serviceStatuses = Object.values(systemStatus.services);
    if (serviceStatuses.some(s => s.status === 'offline' || s.status === 'error')) {
      systemStatus.overall = 'error';
    } else if (serviceStatuses.some(s => s.status === 'degraded' || s.status === 'warning')) {
      systemStatus.overall = 'warning';
    }

    this.systemStatusSubject.next(systemStatus);
  }

  /**
   * Check AI service status (Gemini 2.5 Flash)
   */
  private async checkAIService(): Promise<ServiceStatus> {
    try {
      const startTime = Date.now();

      // TODO: Implement actual AI service health check
      // For now, simulate a check
      await new Promise(resolve => setTimeout(resolve, 100));

      const responseTime = Date.now() - startTime;

      return {
        status: 'online',
        responseTime,
        uptime: 99.9,
        version: 'Gemini 2.5 Flash'
      };
    } catch (error) {
      return {
        status: 'offline',
        lastError: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check database service status (Firestore)
   */
  private async checkDatabaseService(): Promise<ServiceStatus> {
    try {
      const startTime = Date.now();

      // Test Firestore connection using FirebaseContextService
      await this.firebaseContext.getDocument('system/health-check').toPromise();

      const responseTime = Date.now() - startTime;

      return {
        status: 'online',
        responseTime,
        uptime: 99.95,
        version: 'Firestore'
      };
    } catch (error) {
      return {
        status: 'offline',
        lastError: error instanceof Error ? error.message : 'Database connection failed'
      };
    }
  }

  /**
   * Check PWA service status
   */
  private async checkPWAService(): Promise<ServiceStatus> {
    try {
      const registration = await navigator.serviceWorker.getRegistration();

      if (registration && registration.active) {
        return {
          status: 'online',
          uptime: 100,
          version: 'Service Worker Active'
        };
      } else {
        return {
          status: 'degraded',
          lastError: 'Service Worker not active'
        };
      }
    } catch (error) {
      return {
        status: 'offline',
        lastError: 'PWA not supported or service worker failed'
      };
    }
  }

  /**
   * Check authentication service status
   */
  private async checkAuthService(): Promise<ServiceStatus> {
    try {
      // Check if user is authenticated by checking the current user profile
      const userProfile = await this.authService.userProfile$.pipe().toPromise();

      return {
        status: userProfile ? 'online' : 'degraded',
        uptime: 99.9,
        version: 'Firebase Auth'
      };
    } catch (error) {
      return {
        status: 'offline',
        lastError: error instanceof Error ? error.message : 'Auth service failed'
      };
    }
  }

  /**
   * Check storage service status
   */
  private async checkStorageService(): Promise<ServiceStatus> {
    try {
      // Test localStorage availability
      const testKey = 'staffmanager-storage-test';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);

      return {
        status: 'online',
        uptime: 100,
        version: 'Browser Storage'
      };
    } catch (error) {
      return {
        status: 'offline',
        lastError: 'Local storage not available'
      };
    }
  }

  /**
   * Check notification service status
   */
  private async checkNotificationService(): Promise<ServiceStatus> {
    try {
      if ('Notification' in window) {
        const permission = Notification.permission;

        return {
          status: permission === 'granted' ? 'online' : 'degraded',
          uptime: 100,
          version: 'Web Notifications',
          lastError: permission === 'denied' ? 'Notifications blocked by user' : undefined
        };
      } else {
        return {
          status: 'offline',
          lastError: 'Notifications not supported'
        };
      }
    } catch (error) {
      return {
        status: 'offline',
        lastError: 'Notification service failed'
      };
    }
  }

  /**
   * Get settings categories for navigation
   */
  getSettingsCategories(): SettingsCategory[] {
    return [
      {
        id: 'profile',
        name: 'User Profile',
        icon: 'person',
        description: 'Personal account settings and staff profile',
        order: 1,
        sections: [
          { id: 'personal', name: 'Personal Information', description: 'Name, contact details, photo', order: 1 },
          { id: 'preferences', name: 'Preferences', description: 'Language, timezone, defaults', order: 2 },
          { id: 'availability', name: 'Availability', description: 'Work schedule and availability', order: 3 }
        ]
      },
      {
        id: 'business',
        name: 'Business Profiles',
        icon: 'business',
        description: 'Manage multiple businesses and switch between them',
        order: 2,
        requiresPermission: ['manager', 'admin'],
        sections: [
          { id: 'current', name: 'Current Business', description: 'Active business settings', order: 1 },
          { id: 'manage', name: 'Manage Businesses', description: 'Add, edit, or remove businesses', order: 2 },
          { id: 'switch', name: 'Switch Business', description: 'Change active business context', order: 3 }
        ]
      },
      {
        id: 'appearance',
        name: 'Appearance',
        icon: 'palette',
        description: 'Customize the look and feel of StaffManager',
        order: 3,
        sections: [
          { id: 'theme', name: 'Theme', description: 'Light, dark, or system theme', order: 1 },
          { id: 'colors', name: 'Colors', description: 'Custom color schemes', order: 2 },
          { id: 'layout', name: 'Layout', description: 'Density, spacing, and layout options', order: 3 },
          { id: 'accessibility', name: 'Accessibility', description: 'High contrast, motion, and focus options', order: 4 }
        ]
      },
      {
        id: 'notifications',
        name: 'Notifications',
        icon: 'notifications',
        description: 'Email, push, and in-app notification preferences',
        order: 4,
        sections: [
          { id: 'email', name: 'Email Notifications', description: 'Configure email alerts', order: 1 },
          { id: 'push', name: 'Push Notifications', description: 'Mobile and desktop notifications', order: 2 },
          { id: 'inapp', name: 'In-App Notifications', description: 'Notification center settings', order: 3 }
        ]
      },
      {
        id: 'security',
        name: 'Security & Privacy',
        icon: 'security',
        description: 'Password, two-factor auth, and privacy controls',
        order: 5,
        sections: [
          { id: 'password', name: 'Password', description: 'Change password and security settings', order: 1 },
          { id: 'twofactor', name: 'Two-Factor Auth', description: 'Enable additional security', order: 2 },
          { id: 'privacy', name: 'Privacy', description: 'Data sharing and visibility controls', order: 3 }
        ]
      },
      {
        id: 'time',
        name: 'Time & Scheduling',
        icon: 'schedule',
        description: 'Time zones, calendar, and scheduling preferences',
        order: 6,
        sections: [
          { id: 'timezone', name: 'Time Zone', description: 'Time zone and date format settings', order: 1 },
          { id: 'calendar', name: 'Calendar', description: 'Calendar view and integration settings', order: 2 },
          { id: 'scheduling', name: 'Scheduling', description: 'Default shift and scheduling preferences', order: 3 }
        ]
      },
      {
        id: 'data',
        name: 'Data Management',
        icon: 'storage',
        description: 'Export, backup, and data retention settings',
        order: 7,
        requiresPermission: ['manager', 'admin'],
        sections: [
          { id: 'export', name: 'Export Data', description: 'Download your data in various formats', order: 1 },
          { id: 'backup', name: 'Backup', description: 'Automatic backup settings', order: 2 },
          { id: 'retention', name: 'Data Retention', description: 'How long to keep different types of data', order: 3 }
        ]
      },
      {
        id: 'integrations',
        name: 'Integrations',
        icon: 'extension',
        description: 'Connect with third-party services',
        order: 8,
        requiresPermission: ['manager', 'admin'],
        sections: [
          { id: 'calendar', name: 'Calendar Apps', description: 'Google Calendar, Outlook, Apple Calendar', order: 1 },
          { id: 'payroll', name: 'Payroll Systems', description: 'QuickBooks, ADP, Paychex, Gusto', order: 2 },
          { id: 'communication', name: 'Communication', description: 'Slack, Microsoft Teams', order: 3 }
        ]
      },
      {
        id: 'systems',
        name: 'Systems Check',
        icon: 'health_and_safety',
        description: 'Monitor AI, database, and PWA connections',
        order: 9,
        sections: [
          { id: 'status', name: 'System Status', description: 'Real-time service health monitoring', order: 1 },
          { id: 'diagnostics', name: 'Diagnostics', description: 'Run system diagnostics and tests', order: 2 },
          { id: 'troubleshooting', name: 'Troubleshooting', description: 'Common issues and solutions', order: 3 }
        ]
      },
      {
        id: 'advanced',
        name: 'Advanced',
        icon: 'tune',
        description: 'Developer options and experimental features',
        order: 10,
        requiresPermission: ['admin'],
        sections: [
          { id: 'developer', name: 'Developer Options', description: 'Debug mode and performance metrics', order: 1 },
          { id: 'features', name: 'Feature Flags', description: 'Enable or disable features', order: 2 },
          { id: 'experimental', name: 'Experimental', description: 'Try new features before they\'re released', order: 3 }
        ]
      }
    ];
  }

  /**
   * Convert Firestore data to UserSettings
   */
  private convertFirestoreToSettings(data: any): UserSettings {
    return {
      ...data,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date()
    };
  }

  /**
   * Convert UserSettings to Firestore data
   */
  private convertSettingsToFirestore(settings: UserSettings): any {
    return {
      ...settings,
      createdAt: Timestamp.fromDate(settings.createdAt),
      updatedAt: Timestamp.fromDate(settings.updatedAt)
    };
  }

  /**
   * Reset settings to defaults
   */
  async resetToDefaults(userId: string): Promise<void> {
    const defaultSettings: UserSettings = {
      userId,
      ...DEFAULT_USER_SETTINGS,
      createdAt: new Date(),
      updatedAt: new Date(),
      version: '1.0.0'
    } as UserSettings;

    await this.saveUserSettings(defaultSettings);
  }

  /**
   * Export user settings
   */
  exportSettings(): string {
    const settings = this.userSettingsSubject.value;
    if (!settings) {
      throw new Error('No settings to export');
    }

    return JSON.stringify(settings, null, 2);
  }

  /**
   * Import user settings
   */
  async importSettings(settingsJson: string): Promise<void> {
    try {
      const settings = JSON.parse(settingsJson) as UserSettings;
      settings.updatedAt = new Date();
      await this.saveUserSettings(settings);
    } catch (error) {
      throw new Error('Invalid settings format');
    }
  }
}
