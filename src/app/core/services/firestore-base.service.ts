import { Injectable, inject } from '@angular/core';
import {
  Firestore,
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  DocumentReference,
  QueryConstraint,
  Timestamp,
  onSnapshot,
  DocumentSnapshot,
  QuerySnapshot
} from '@angular/fire/firestore';
import { Observable, from, map } from 'rxjs';

export interface FirestoreDocument {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

@Injectable({
  providedIn: 'root'
})
export class FirestoreBaseService {
  protected firestore = inject(Firestore);

  // Convert Firestore data to typed objects
  protected convertTimestamps<T extends FirestoreDocument>(data: any): T {
    const converted = { ...data };

    // Convert Firestore Timestamps to Dates
    Object.keys(converted).forEach(key => {
      if (converted[key] instanceof Timestamp) {
        converted[key] = converted[key].toDate();
      } else if (converted[key] && typeof converted[key] === 'object') {
        // Handle nested objects
        if (converted[key].seconds && converted[key].nanoseconds) {
          converted[key] = new Timestamp(converted[key].seconds, converted[key].nanoseconds).toDate();
        }
      }
    });

    return converted as T;
  }

  // Convert Date objects to Firestore Timestamps for saving
  protected prepareForFirestore(data: any): any {
    const prepared = { ...data };

    Object.keys(prepared).forEach(key => {
      if (prepared[key] instanceof Date) {
        prepared[key] = Timestamp.fromDate(prepared[key]);
      } else if (Array.isArray(prepared[key])) {
        prepared[key] = prepared[key].map((item: any) =>
          item instanceof Date ? Timestamp.fromDate(item) : item
        );
      } else if (prepared[key] && typeof prepared[key] === 'object') {
        prepared[key] = this.prepareForFirestore(prepared[key]);
      }
    });

    return prepared;
  }

  // Generic CRUD operations
  protected async create<T extends FirestoreDocument>(
    collectionName: string,
    data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<T> {
    const now = new Date();
    const docData = {
      ...data,
      createdAt: now,
      updatedAt: now
    };

    const preparedData = this.prepareForFirestore(docData);
    const docRef = await addDoc(collection(this.firestore, collectionName), preparedData);

    return {
      ...docData,
      id: docRef.id
    } as T;
  }

  protected async update<T extends FirestoreDocument>(
    collectionName: string,
    id: string,
    data: Partial<Omit<T, 'id' | 'createdAt'>>
  ): Promise<void> {
    const updateData = {
      ...data,
      updatedAt: new Date()
    };

    const preparedData = this.prepareForFirestore(updateData);
    const docRef = doc(this.firestore, collectionName, id);
    await updateDoc(docRef, preparedData);
  }

  protected async delete(collectionName: string, id: string): Promise<void> {
    const docRef = doc(this.firestore, collectionName, id);
    await deleteDoc(docRef);
  }

  protected async getById<T extends FirestoreDocument>(
    collectionName: string,
    id: string
  ): Promise<T | null> {
    const docRef = doc(this.firestore, collectionName, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return this.convertTimestamps<T>({
        id: docSnap.id,
        ...data
      } as T);
    }

    return null;
  }

  protected async getAll<T extends FirestoreDocument>(
    collectionName: string,
    constraints: QueryConstraint[] = []
  ): Promise<T[]> {
    const collectionRef = collection(this.firestore, collectionName);
    const q = query(collectionRef, ...constraints);
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc =>
      this.convertTimestamps<T>({
        id: doc.id,
        ...doc.data()
      } as T)
    );
  }

  // Real-time subscriptions
  protected subscribeToDocument<T extends FirestoreDocument>(
    collectionName: string,
    id: string
  ): Observable<T | null> {
    const docRef = doc(this.firestore, collectionName, id);

    return new Observable(observer => {
      const unsubscribe = onSnapshot(docRef, (docSnap) => {
        if (docSnap.exists()) {
          const data = this.convertTimestamps<T>({
            id: docSnap.id,
            ...docSnap.data()
          } as T);
          observer.next(data);
        } else {
          observer.next(null);
        }
      }, (error) => {
        observer.error(error);
      });

      return () => unsubscribe();
    });
  }

  protected subscribeToCollection<T extends FirestoreDocument>(
    collectionName: string,
    constraints: QueryConstraint[] = []
  ): Observable<T[]> {
    const collectionRef = collection(this.firestore, collectionName);
    const q = query(collectionRef, ...constraints);

    return new Observable(observer => {
      const unsubscribe = onSnapshot(q, (querySnapshot) => {
        const documents = querySnapshot.docs.map(doc =>
          this.convertTimestamps<T>({
            id: doc.id,
            ...doc.data()
          } as T)
        );
        observer.next(documents);
      }, (error) => {
        observer.error(error);
      });

      return () => unsubscribe();
    });
  }

  // Utility methods for common query patterns
  protected createWhereConstraint(field: string, operator: any, value: any): QueryConstraint {
    return where(field, operator, value);
  }

  protected createOrderByConstraint(field: string, direction: 'asc' | 'desc' = 'asc'): QueryConstraint {
    return orderBy(field, direction);
  }

  protected createLimitConstraint(limitCount: number): QueryConstraint {
    return limit(limitCount);
  }

  // Batch operations
  protected async batchCreate<T extends FirestoreDocument>(
    collectionName: string,
    items: Omit<T, 'id' | 'createdAt' | 'updatedAt'>[]
  ): Promise<T[]> {
    const promises = items.map(item => this.create<T>(collectionName, item));
    return Promise.all(promises);
  }

  protected async batchUpdate<T extends FirestoreDocument>(
    collectionName: string,
    updates: { id: string; data: Partial<Omit<T, 'id' | 'createdAt'>> }[]
  ): Promise<void> {
    const promises = updates.map(update =>
      this.update<T>(collectionName, update.id, update.data)
    );
    await Promise.all(promises);
  }

  protected async batchDelete(
    collectionName: string,
    ids: string[]
  ): Promise<void> {
    const promises = ids.map(id => this.delete(collectionName, id));
    await Promise.all(promises);
  }
}
