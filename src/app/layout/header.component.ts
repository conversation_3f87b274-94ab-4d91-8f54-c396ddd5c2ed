import { Component, EventEmitter, Output, Input } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatBadgeModule } from '@angular/material/badge';
import { CommonModule, DatePipe } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, MatToolbarModule, MatIconModule, MatButtonModule, MatBadgeModule, MatMenuModule, MatDividerModule],
  providers: [DatePipe],
  template: `
    <mat-toolbar color="primary" class="app-header">
      <button mat-icon-button
              *ngIf="showMenuButton"
              (click)="menuToggle.emit()"
              aria-label="Open sidebar"
              class="icon-btn">
        <mat-icon>menu</mat-icon>
      </button>
      <span class="date-time-pill">
        <span class="date">{{ today | date:'MMMM d, yyyy' }}</span>
        <span class="time">{{ now | date:'h:mm a' }}</span>
      </span>
      <span class="app-title">StaffManager</span>
      <span class="spacer"></span>
      <button mat-icon-button [matMenuTriggerFor]="notificationsMenu" aria-label="Notifications" class="icon-btn">
        <mat-icon matBadge="3" matBadgeColor="warn" aria-hidden="false" role="img" aria-label="3 new notifications">notifications</mat-icon>
      </button>
      <mat-menu #notificationsMenu="matMenu">
        <button mat-menu-item *ngFor="let n of notifications">
          <mat-icon color="primary">notifications</mat-icon>
          <span>{{ n }}</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item>View all notifications</button>
      </mat-menu>
      <button mat-icon-button [matMenuTriggerFor]="chatMenu" aria-label="Chat" class="icon-btn">
        <mat-icon matBadge="2" matBadgeColor="warn" aria-hidden="false" role="img" aria-label="2 unread chat messages">chat</mat-icon>
      </button>
      <mat-menu #chatMenu="matMenu">
        <button mat-menu-item *ngFor="let c of chats">
          <mat-icon color="primary">chat</mat-icon>
          <span>{{ c }}</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item>Open chat</button>
      </mat-menu>
    </mat-toolbar>
  `,
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  @Input() showMenuButton = false;
  @Output() menuToggle = new EventEmitter<void>();
  today = new Date();
  now = new Date();
  notifications = [
    'New staff member added',
    'Meeting scheduled for tomorrow',
    'Payroll exported to ADP'
  ];
  chats = [
    '2 unread messages',
    'Support: New reply'
  ];
}