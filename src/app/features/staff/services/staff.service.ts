import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of, combineLatest } from 'rxjs';
import { map, filter, debounceTime, distinctUntilChanged } from 'rxjs/operators';

import {
  StaffMember,
  StaffFilter,
  StaffSortOptions,
  StaffPaginationOptions,
  StaffListResponse,
  StaffHubProfile,
  StaffPerformanceMetrics
} from '../models/staff.model';

@Injectable({
  providedIn: 'root'
})
export class StaffService {
  private staffSubject = new BehaviorSubject<StaffMember[]>([]);
  private filterSubject = new BehaviorSubject<StaffFilter>({});
  private sortSubject = new BehaviorSubject<StaffSortOptions>({ field: 'lastName', direction: 'asc' });
  private paginationSubject = new BehaviorSubject<StaffPaginationOptions>({ page: 1, pageSize: 20 });

  // Public observables
  staff$ = this.staffSubject.asObservable();
  filter$ = this.filterSubject.asObservable();
  sort$ = this.sortSubject.asObservable();
  pagination$ = this.paginationSubject.asObservable();

  // Filtered and sorted staff list
  filteredStaff$ = combineLatest([
    this.staff$,
    this.filter$.pipe(debounceTime(300), distinctUntilChanged()),
    this.sort$
  ]).pipe(
    map(([staff, filter, sort]) => this.applyFiltersAndSort(staff, filter, sort))
  );

  // Paginated staff list
  paginatedStaff$: Observable<StaffListResponse> = combineLatest([
    this.filteredStaff$,
    this.pagination$,
    this.filter$,
    this.sort$
  ]).pipe(
    map(([staff, pagination, filter, sort]) => this.applyPagination(staff, pagination, filter, sort))
  );

  constructor() {
    this.loadMockData();
  }

  // Staff CRUD Operations
  getAllStaff(): Observable<StaffMember[]> {
    return this.staff$;
  }

  getStaffById(id: string): Observable<StaffMember | undefined> {
    return this.staff$.pipe(
      map(staff => staff.find(s => s.id === id))
    );
  }

  createStaff(staff: Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>): Observable<StaffMember> {
    const newStaff: StaffMember = {
      ...staff,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const currentStaff = this.staffSubject.value;
    this.staffSubject.next([...currentStaff, newStaff]);

    return of(newStaff);
  }

  updateStaff(id: string, updates: Partial<StaffMember>): Observable<StaffMember | null> {
    const currentStaff = this.staffSubject.value;
    const index = currentStaff.findIndex(s => s.id === id);

    if (index === -1) {
      return of(null);
    }

    const updatedStaff = {
      ...currentStaff[index],
      ...updates,
      updatedAt: new Date()
    };

    const newStaffList = [...currentStaff];
    newStaffList[index] = updatedStaff;
    this.staffSubject.next(newStaffList);

    return of(updatedStaff);
  }

  deleteStaff(id: string): Observable<boolean> {
    const currentStaff = this.staffSubject.value;
    const filteredStaff = currentStaff.filter(s => s.id !== id);

    if (filteredStaff.length === currentStaff.length) {
      return of(false); // Staff member not found
    }

    this.staffSubject.next(filteredStaff);
    return of(true);
  }

  // Filtering and Sorting
  setFilter(filter: StaffFilter): void {
    this.filterSubject.next(filter);
  }

  setSort(sort: StaffSortOptions): void {
    this.sortSubject.next(sort);
  }

  setPagination(pagination: StaffPaginationOptions): void {
    this.paginationSubject.next(pagination);
  }

  // Search functionality
  searchStaff(query: string): Observable<StaffMember[]> {
    return this.staff$.pipe(
      map(staff => staff.filter(s =>
        s.firstName.toLowerCase().includes(query.toLowerCase()) ||
        s.lastName.toLowerCase().includes(query.toLowerCase()) ||
        s.email.toLowerCase().includes(query.toLowerCase()) ||
        s.employeeId.toLowerCase().includes(query.toLowerCase()) ||
        s.position.toLowerCase().includes(query.toLowerCase()) ||
        s.department.toLowerCase().includes(query.toLowerCase())
      ))
    );
  }

  // Staff Hub (Employee Portal) Methods
  getStaffHubProfile(staffId: string): Observable<StaffHubProfile | null> {
    // Mock implementation - in real app, this would fetch from backend
    return of({
      staffId,
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: {
          email: true,
          push: true,
          sms: false
        },
        dashboard: {
          widgets: ['schedule', 'tasks', 'timesheet'],
          layout: {}
        }
      },
      quickActions: ['clock-in', 'view-schedule', 'request-time-off'],
      recentActivity: []
    });
  }

  updateStaffHubProfile(staffId: string, profile: Partial<StaffHubProfile>): Observable<StaffHubProfile | null> {
    // Mock implementation
    return this.getStaffHubProfile(staffId);
  }

  // Performance and Analytics
  getStaffPerformanceMetrics(staffId: string, startDate: Date, endDate: Date): Observable<StaffPerformanceMetrics | null> {
    // Mock implementation
    return of({
      staffId,
      period: { startDate, endDate },
      metrics: {
        attendanceRate: 95.5,
        punctualityScore: 88.2,
        tasksCompleted: 47,
        customerRating: 4.3,
        hoursWorked: 160,
        overtimeHours: 8
      },
      goals: [],
      feedback: []
    });
  }

  // Utility Methods
  getStaffByDepartment(department: string): Observable<StaffMember[]> {
    return this.staff$.pipe(
      map(staff => staff.filter(s => s.department === department))
    );
  }

  getStaffByStatus(status: StaffMember['status']): Observable<StaffMember[]> {
    return this.staff$.pipe(
      map(staff => staff.filter(s => s.status === status))
    );
  }

  getActiveStaff(): Observable<StaffMember[]> {
    return this.getStaffByStatus('active');
  }

  getDepartments(): Observable<string[]> {
    return this.staff$.pipe(
      map(staff => [...new Set(staff.map(s => s.department))].sort())
    );
  }

  getPositions(): Observable<string[]> {
    return this.staff$.pipe(
      map(staff => [...new Set(staff.map(s => s.position))].sort())
    );
  }

  // Private helper methods
  private applyFiltersAndSort(staff: StaffMember[], filter: StaffFilter, sort: StaffSortOptions): StaffMember[] {
    let filtered = [...staff];

    // Apply filters
    if (filter.search) {
      const query = filter.search.toLowerCase();
      filtered = filtered.filter(s =>
        s.firstName.toLowerCase().includes(query) ||
        s.lastName.toLowerCase().includes(query) ||
        s.email.toLowerCase().includes(query) ||
        s.employeeId.toLowerCase().includes(query) ||
        s.position.toLowerCase().includes(query) ||
        s.department.toLowerCase().includes(query)
      );
    }

    if (filter.departments?.length) {
      filtered = filtered.filter(s => filter.departments!.includes(s.department));
    }

    if (filter.positions?.length) {
      filtered = filtered.filter(s => filter.positions!.includes(s.position));
    }

    if (filter.employmentTypes?.length) {
      filtered = filtered.filter(s => filter.employmentTypes!.includes(s.employmentType));
    }

    if (filter.statuses?.length) {
      filtered = filtered.filter(s => filter.statuses!.includes(s.status));
    }

    if (filter.businessIds?.length) {
      filtered = filtered.filter(s =>
        filter.businessIds!.some(businessId => s.businessIds.includes(businessId))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sort.field];
      const bValue = b[sort.field];

      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sort.direction === 'asc' ? 1 : -1;
      if (bValue == null) return sort.direction === 'asc' ? -1 : 1;

      if (aValue < bValue) return sort.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sort.direction === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }

  private applyPagination(
    staff: StaffMember[],
    pagination: StaffPaginationOptions,
    filter: StaffFilter,
    sort: StaffSortOptions
  ): StaffListResponse {
    const totalCount = staff.length;
    const totalPages = Math.ceil(totalCount / pagination.pageSize);
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    const paginatedStaff = staff.slice(startIndex, endIndex);

    return {
      staff: paginatedStaff,
      pagination: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        totalCount,
        totalPages
      },
      filters: filter,
      sort
    };
  }

  private generateId(): string {
    return 'staff_' + Math.random().toString(36).substr(2, 9);
  }

  private loadMockData(): void {
    const mockStaff: StaffMember[] = [
      {
        id: 'staff_001',
        employeeId: 'EMP001',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        hireDate: new Date('2023-01-15'),
        position: 'Senior Developer',
        department: 'Engineering',
        employmentType: 'full-time',
        status: 'active',
        hourlyRate: 45,
        skills: [
          { id: 'skill_1', name: 'JavaScript', category: 'Programming', level: 'expert', verified: true },
          { id: 'skill_2', name: 'React', category: 'Frontend', level: 'advanced', verified: true }
        ],
        certifications: [],
        education: [],
        workExperience: [],
        availability: {
          monday: { available: true, startTime: '09:00', endTime: '17:00' },
          tuesday: { available: true, startTime: '09:00', endTime: '17:00' },
          wednesday: { available: true, startTime: '09:00', endTime: '17:00' },
          thursday: { available: true, startTime: '09:00', endTime: '17:00' },
          friday: { available: true, startTime: '09:00', endTime: '17:00' },
          saturday: { available: false },
          sunday: { available: false }
        },
        timeZone: 'America/New_York',
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date(),
        createdBy: 'admin',
        businessIds: ['business_1'],
        primaryBusinessId: 'business_1',
        roles: ['developer'],
        permissions: ['read', 'write'],
        accessLevel: 'advanced'
      }
      // Add more mock staff members as needed
    ];

    this.staffSubject.next(mockStaff);
  }
}
