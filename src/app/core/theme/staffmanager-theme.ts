import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

/**
 * StaffManager Theme Configuration
 * Based on the design patterns from previous iterations
 */

export interface StaffManagerTheme {
  primary: {
    main: string;
    light: string;
    dark: string;
    contrastText: string;
  };
  secondary: {
    main: string;
    light: string;
    dark: string;
    contrastText: string;
  };
  accent: {
    main: string;
    light: string;
    dark: string;
  };
  background: {
    default: string;
    paper: string;
    surface: string;
  };
  text: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  spacing: {
    unit: number;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    small: string;
    medium: string;
    large: string;
  };
  shadows: {
    light: string;
    medium: string;
    heavy: string;
  };
}

export const STAFFMANAGER_LIGHT_THEME: StaffManagerTheme = {
  primary: {
    main: '#1976d2',      // Blue 700
    light: '#42a5f5',     // Blue 400
    dark: '#1565c0',      // Blue 800
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#9c27b0',      // Purple 500
    light: '#ba68c8',     // Purple 300
    dark: '#7b1fa2',      // Purple 700
    contrastText: '#ffffff',
  },
  accent: {
    main: '#ff6b35',      // Orange accent
    light: '#ff8a65',
    dark: '#e64a19',
  },
  background: {
    default: '#f7f9fa',   // Light gray background
    paper: '#ffffff',     // White cards/surfaces
    surface: '#fafbfc',   // Slightly off-white
  },
  text: {
    primary: '#1a1a1a',   // Near black
    secondary: '#666666', // Medium gray
    disabled: '#999999',  // Light gray
  },
  spacing: {
    unit: 8,
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
  },
  shadows: {
    light: '0 2px 4px rgba(0,0,0,0.1)',
    medium: '0 4px 8px rgba(0,0,0,0.12)',
    heavy: '0 8px 16px rgba(0,0,0,0.15)',
  },
};

export const STAFFMANAGER_DARK_THEME: StaffManagerTheme = {
  primary: {
    main: '#42a5f5',      // Lighter blue for dark mode
    light: '#64b5f6',
    dark: '#1976d2',
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#ba68c8',      // Lighter purple for dark mode
    light: '#ce93d8',
    dark: '#9c27b0',
    contrastText: '#ffffff',
  },
  accent: {
    main: '#ff8a65',      // Lighter orange for dark mode
    light: '#ffab91',
    dark: '#ff6b35',
  },
  background: {
    default: '#121212',   // Dark background
    paper: '#1e1e1e',     // Dark cards/surfaces
    surface: '#2a2a2a',   // Slightly lighter dark
  },
  text: {
    primary: '#ffffff',   // White text
    secondary: '#b3b3b3', // Light gray
    disabled: '#666666',  // Medium gray
  },
  spacing: {
    unit: 8,
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
  },
  shadows: {
    light: '0 2px 4px rgba(0,0,0,0.3)',
    medium: '0 4px 8px rgba(0,0,0,0.4)',
    heavy: '0 8px 16px rgba(0,0,0,0.5)',
  },
};

@Injectable({
  providedIn: 'root'
})
export class StaffManagerThemeService {
  private currentTheme: StaffManagerTheme = STAFFMANAGER_LIGHT_THEME;
  private isDarkMode = false;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  getCurrentTheme(): StaffManagerTheme {
    return this.currentTheme;
  }

  toggleTheme(): void {
    this.isDarkMode = !this.isDarkMode;
    this.currentTheme = this.isDarkMode ? STAFFMANAGER_DARK_THEME : STAFFMANAGER_LIGHT_THEME;
    this.applyThemeToDocument();
  }

  setTheme(isDark: boolean): void {
    this.isDarkMode = isDark;
    this.currentTheme = isDark ? STAFFMANAGER_DARK_THEME : STAFFMANAGER_LIGHT_THEME;
    this.applyThemeToDocument();
  }

  private applyThemeToDocument(): void {
    // CRITICAL FIX: Only apply theme changes in browser environment
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const root = document.documentElement;
    const theme = this.currentTheme;

    // Apply CSS custom properties
    root.style.setProperty('--sm-primary-main', theme.primary.main);
    root.style.setProperty('--sm-primary-light', theme.primary.light);
    root.style.setProperty('--sm-primary-dark', theme.primary.dark);
    root.style.setProperty('--sm-secondary-main', theme.secondary.main);
    root.style.setProperty('--sm-accent-main', theme.accent.main);
    root.style.setProperty('--sm-background-default', theme.background.default);
    root.style.setProperty('--sm-background-paper', theme.background.paper);
    root.style.setProperty('--sm-text-primary', theme.text.primary);
    root.style.setProperty('--sm-text-secondary', theme.text.secondary);
    root.style.setProperty('--sm-spacing-md', theme.spacing.md);
    root.style.setProperty('--sm-border-radius-medium', theme.borderRadius.medium);
    root.style.setProperty('--sm-shadow-medium', theme.shadows.medium);

    // Toggle dark mode class
    if (this.isDarkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }

  isDark(): boolean {
    return this.isDarkMode;
  }
}
