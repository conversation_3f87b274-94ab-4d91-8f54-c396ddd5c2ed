import { Injectable, inject } from '@angular/core';
import { Observable, from, switchMap, map, of, combineLatest } from 'rxjs';
import { AuthService, UserProfile } from '../auth/auth.service';
import { DataInitializationService } from './data-initialization.service';
import { BusinessProfileService } from './business-profile.service';
import { StaffFirestoreService } from '../../features/staff/services/staff-firestore.service';

/**
 * User Data Migration Service
 *
 * Handles migration and setup for existing users who may not have
 * complete profile data (business profiles, staff profiles, etc.)
 */
@Injectable({
  providedIn: 'root'
})
export class UserDataMigrationService {
  private authService = inject(AuthService);
  private dataInitService = inject(DataInitializationService);
  private businessService = inject(BusinessProfileService);
  private staffService = inject(StaffFirestoreService);

  /**
   * Check and migrate user data if needed
   */
  checkAndMigrateUserData(): Observable<boolean> {
    return this.authService.userProfile$.pipe(
      switchMap(userProfile => {
        if (!userProfile) {
          return of(false);
        }

        return this.needsDataMigration(userProfile).pipe(
          switchMap(needsMigration => {
            if (needsMigration) {
              console.log('🔄 User needs data migration, initializing...', userProfile.email);
              return this.migrateUserData(userProfile);
            }
            return of(true);
          })
        );
      })
    );
  }

  /**
   * Check if user needs data migration
   */
  private needsDataMigration(userProfile: UserProfile): Observable<boolean> {
    // Check if user has business and staff associations
    const hasBusinessId = userProfile.primaryBusinessId && userProfile.primaryBusinessId.length > 0;
    const hasStaffId = userProfile.staffId && userProfile.staffId.length > 0;

    if (!hasBusinessId || !hasStaffId) {
      return of(true);
    }

    // Check if business profile exists
    return this.businessService.selectedBusinessProfile$.pipe(
      map(businessProfile => {
        return !businessProfile;
      })
    );
  }

  /**
   * Migrate user data
   */
  private migrateUserData(userProfile: UserProfile): Observable<boolean> {
    return this.dataInitService.initializeNewUser(userProfile).pipe(
      map(() => {
        console.log('✅ User data migration completed for:', userProfile.email);
        return true;
      }),
      switchMap(() => {
        // Reload user profile to get updated data
        return this.authService.userProfile$.pipe(
          map(() => true)
        );
      })
    );
  }

  /**
   * Force refresh user data
   */
  refreshUserData(): Observable<boolean> {
    return this.authService.userProfile$.pipe(
      switchMap(userProfile => {
        if (!userProfile) {
          return of(false);
        }

        // Force reload business profile
        if (userProfile.primaryBusinessId) {
          this.businessService.loadBusinessProfile(userProfile.primaryBusinessId);
        }

        return of(true);
      })
    );
  }

  /**
   * Create sample data for testing
   */
  createSampleData(userProfile: UserProfile): Observable<boolean> {
    if (!userProfile.primaryBusinessId) {
      console.error('Cannot create sample data: No business ID');
      return of(false);
    }

    console.log('🎯 Creating sample data for business:', userProfile.primaryBusinessId);

    // Create sample staff members
    const sampleStaff = this.createSampleStaffMembers(userProfile);

    // Create sample tasks
    const sampleTasks = this.createSampleTasks(userProfile);

    return combineLatest([
      ...sampleStaff.map(staff => this.staffService.createStaff(staff)),
      // Add sample tasks creation here when task service is ready
    ]).pipe(
      map(() => {
        console.log('✅ Sample data created successfully');
        return true;
      })
    );
  }

  /**
   * Create sample staff members
   */
  private createSampleStaffMembers(userProfile: UserProfile): any[] {
    const now = new Date();
    const businessId = userProfile.primaryBusinessId;

    return [
      {
        employeeId: 'EMP-001',
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '(*************',
        hireDate: new Date(2023, 0, 15),
        position: 'Sales Associate',
        department: 'Sales',
        employmentType: 'full-time',
        status: 'active',
        skills: [
          { name: 'Customer Service', level: 'advanced', verified: true },
          { name: 'Sales', level: 'intermediate', verified: true }
        ],
        certifications: [],
        education: [],
        workExperience: [],
        availability: this.createDefaultAvailability(),
        timeZone: 'America/New_York',
        createdBy: userProfile.uid,
        businessIds: [businessId],
        primaryBusinessId: businessId,
        roles: ['staff'],
        permissions: ['view_own_profile', 'request_time_off', 'view_schedule'],
        accessLevel: 'basic',
        tags: ['sample-data']
      },
      {
        employeeId: 'EMP-002',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '(*************',
        hireDate: new Date(2023, 2, 1),
        position: 'Shift Supervisor',
        department: 'Operations',
        employmentType: 'full-time',
        status: 'active',
        skills: [
          { name: 'Leadership', level: 'advanced', verified: true },
          { name: 'Operations Management', level: 'intermediate', verified: true }
        ],
        certifications: [],
        education: [],
        workExperience: [],
        availability: this.createDefaultAvailability(),
        timeZone: 'America/New_York',
        createdBy: userProfile.uid,
        businessIds: [businessId],
        primaryBusinessId: businessId,
        roles: ['manager'],
        permissions: ['manage_staff', 'view_reports', 'manage_schedules', 'approve_requests'],
        accessLevel: 'advanced',
        tags: ['sample-data']
      }
    ];
  }

  /**
   * Create sample tasks
   */
  private createSampleTasks(userProfile: UserProfile): any[] {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return [
      {
        title: 'Complete Monthly Inventory',
        description: 'Conduct full inventory count for all products',
        priority: 'high',
        category: 'administrative',
        status: 'pending',
        assignedTo: [userProfile.staffId],
        assignedBy: userProfile.uid,
        createdBy: userProfile.uid,
        dueDate: tomorrow,
        estimatedHours: 4,
        businessId: userProfile.primaryBusinessId,
        tags: ['sample-data', 'inventory']
      },
      {
        title: 'Update Staff Training Materials',
        description: 'Review and update training documentation',
        priority: 'medium',
        category: 'training',
        status: 'pending',
        assignedTo: [userProfile.staffId],
        assignedBy: userProfile.uid,
        createdBy: userProfile.uid,
        dueDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 1 week
        estimatedHours: 2,
        businessId: userProfile.primaryBusinessId,
        tags: ['sample-data', 'training']
      }
    ];
  }

  /**
   * Create default availability
   */
  private createDefaultAvailability(): any {
    const defaultDay = {
      isAvailable: true,
      timeSlots: [{ startTime: '09:00', endTime: '17:00' }]
    };

    return {
      weeklyAvailability: {
        monday: defaultDay,
        tuesday: defaultDay,
        wednesday: defaultDay,
        thursday: defaultDay,
        friday: defaultDay,
        saturday: { isAvailable: false, timeSlots: [] },
        sunday: { isAvailable: false, timeSlots: [] }
      },
      timeOffRequests: [],
      blackoutDates: [],
      preferredShifts: [],
      maxHoursPerWeek: 40,
      minHoursPerWeek: 20
    };
  }
}
