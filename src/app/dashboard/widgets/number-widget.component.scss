.number-widget {
  min-width: 200px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 16px;
  background: var(--mui-palette-background-paper, #fff);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.number-widget.quicklook {
  min-width: 120px;
  min-height: 80px;
  padding: 8px;
}

.number-widget-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--mui-palette-text-primary, #222);
}

.number-widget-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--mui-palette-primary-main, #1976d2);
  margin: 8px 0 4px 0;
}

.number-widget-description {
  font-size: 0.95rem;
  color: var(--mui-palette-text-secondary, #666);
} 