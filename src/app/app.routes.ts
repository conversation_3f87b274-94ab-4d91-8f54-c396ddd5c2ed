import { Routes } from '@angular/router';
import { LayoutComponent } from './layout/layout.component';
import { AuthGuard, AdminGuard, ManagerGuard, StaffEditGuard } from './core/auth/auth.guard';

export const routes: Routes = [
  // Authentication routes (no layout)
  {
    path: 'auth',
    children: [
      {
        path: 'login',
        loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
      },
      {
        path: 'register',
        loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent)
      },
      { path: '', redirectTo: 'login', pathMatch: 'full' }
    ]
  },
  // Emergency fix route (not protected by auth guard)
  {
    path: 'emergency-fix',
    loadComponent: () => import('./emergency-fix.component').then(m => m.EmergencyFixComponent)
  },
  // Protected routes (with layout) - GUARDS RE-ENABLED
  {
    path: '',
    component: LayoutComponent,
    canActivate: [AuthGuard], // RE-ENABLED - Authentication is working
    children: [
      {
        path: 'dashboard',
        loadComponent: () => import('./features/dashboard/enhanced-dashboard.component').then(m => m.EnhancedDashboardComponent)
      },
      {
        path: 'staff',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/staff/staff-list/staff-list.component').then(m => m.StaffListComponent)
          },
          {
            path: 'my-profile',
            loadComponent: () => import('./features/staff/staff-profile-redirect.component').then(m => m.StaffProfileRedirectComponent)
          },
          {
            path: 'profile/:id',
            loadComponent: () => import('./features/staff/staff-profile/staff-profile.component').then(m => m.StaffProfileComponent)
          },
          {
            path: 'create',
            loadComponent: () => import('./features/staff/staff-form/staff-form.component').then(m => m.StaffFormComponent)
            // canActivate: [ManagerGuard] // TEMPORARILY DISABLED
          },
          {
            path: 'edit/:id',
            loadComponent: () => import('./features/staff/staff-form/staff-form.component').then(m => m.StaffFormComponent)
            // Temporarily removed guard to fix login redirect issue
          }
        ]
      },
      {
        path: 'calendar',
        loadComponent: () => import('./features/calendar/calendar.component').then(m => m.CalendarComponent)
      },
      {
        path: 'tasks',
        loadComponent: () => import('./features/tasks/tasks.component').then(m => m.TasksComponent)
      },
      {
        path: 'goals',
        loadChildren: () => import('./features/goals/goals.routes').then(m => m.goalsRoutes)
      },
      {
        path: 'time',
        loadComponent: () => import('./features/time-management/time-management.component').then(m => m.TimeManagementComponent)
      },
      {
        path: 'business',
        children: [
          {
            path: 'profile',
            loadComponent: () => import('./features/business-profile/business-profile-settings.component').then(m => m.BusinessProfileSettingsComponent)
            // canActivate: [ManagerGuard] // TEMPORARILY DISABLED
          }
        ]
      },
      {
        path: 'settings',
        loadComponent: () => import('./features/settings/settings.component').then(m => m.SettingsComponent)
      },
      {
        path: 'debug/firebase-test',
        loadComponent: () => import('./debug/firebase-test.component').then(m => m.FirebaseTestComponent)
      },
      {
        path: 'debug/auth-test',
        loadComponent: () => import('./debug/auth-test.component').then(m => m.AuthTestComponent)
      },
      {
        path: 'emergency-fix',
        loadComponent: () => import('./emergency-fix.component').then(m => m.EmergencyFixComponent)
      },
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
    ]
  },
  { path: '**', redirectTo: 'auth/login' }
];
