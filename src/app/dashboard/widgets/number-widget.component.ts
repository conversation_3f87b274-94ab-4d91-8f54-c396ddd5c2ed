import { Component, Input } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-number-widget',
  standalone: true,
  imports: [MatCardModule, CommonModule],
  template: `
    <mat-card class="number-widget" [ngClass]="{ 'quicklook': quickLook }">
      <div class="number-widget-content">
        <div class="number-widget-title">{{ title }}</div>
        <div class="number-widget-value">{{ value }}</div>
        <div class="number-widget-description">{{ description }}</div>
      </div>
    </mat-card>
  `,
  styleUrls: ['./number-widget.component.scss']
})
export class NumberWidgetComponent {
  @Input() title = '';
  @Input() value: number | string = '';
  @Input() description = '';
  @Input() color: 'primary' | 'accent' | 'warn' | string = 'primary';
  @Input() quickLook = false;
} 