import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, combineLatest } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { 
  Firestore, 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs,
  onSnapshot,
  Timestamp 
} from '@angular/fire/firestore';

import { 
  BusinessProfile, 
  BusinessStatus, 
  BusinessMetrics,
  HoursOfOperation,
  DayHours 
} from '../models/business.model';
import { AuthService } from '../auth/auth.service';

@Injectable({
  providedIn: 'root'
})
export class BusinessProfileService {
  private firestore = inject(Firestore);
  private authService = inject(AuthService);

  private selectedBusinessProfileSubject = new BehaviorSubject<BusinessProfile | null>(null);
  public selectedBusinessProfile$ = this.selectedBusinessProfileSubject.asObservable();

  private businessProfilesSubject = new BehaviorSubject<BusinessProfile[]>([]);
  public businessProfiles$ = this.businessProfilesSubject.asObservable();

  constructor() {
    // Load user's business profiles when authenticated
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.primaryBusinessId) {
        this.loadBusinessProfile(profile.primaryBusinessId);
        this.loadUserBusinessProfiles(profile.businessIds || [profile.primaryBusinessId]);
      }
    });
  }

  /**
   * Load a specific business profile
   */
  async loadBusinessProfile(businessId: string): Promise<BusinessProfile | null> {
    try {
      const docRef = doc(this.firestore, 'businessProfiles', businessId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        const businessProfile = this.convertFirestoreToBusinessProfile(data);
        this.selectedBusinessProfileSubject.next(businessProfile);
        return businessProfile;
      } else {
        // Create default business profile if none exists
        const defaultProfile = this.createDefaultBusinessProfile(businessId);
        await this.saveBusinessProfile(defaultProfile);
        this.selectedBusinessProfileSubject.next(defaultProfile);
        return defaultProfile;
      }
    } catch (error) {
      console.error('Error loading business profile:', error);
      return null;
    }
  }

  /**
   * Load all business profiles for a user
   */
  async loadUserBusinessProfiles(businessIds: string[]): Promise<void> {
    try {
      const profiles: BusinessProfile[] = [];
      
      for (const businessId of businessIds) {
        const profile = await this.loadBusinessProfile(businessId);
        if (profile) {
          profiles.push(profile);
        }
      }
      
      this.businessProfilesSubject.next(profiles);
    } catch (error) {
      console.error('Error loading user business profiles:', error);
    }
  }

  /**
   * Save business profile to Firestore
   */
  async saveBusinessProfile(businessProfile: BusinessProfile): Promise<void> {
    try {
      const docRef = doc(this.firestore, 'businessProfiles', businessProfile.id);
      const firestoreData = this.convertBusinessProfileToFirestore(businessProfile);
      await setDoc(docRef, firestoreData, { merge: true });
      
      // Update local state
      this.selectedBusinessProfileSubject.next(businessProfile);
    } catch (error) {
      console.error('Error saving business profile:', error);
      throw error;
    }
  }

  /**
   * Update specific business profile fields
   */
  async updateBusinessProfile(businessId: string, updates: Partial<BusinessProfile>): Promise<void> {
    try {
      const docRef = doc(this.firestore, 'businessProfiles', businessId);
      const firestoreUpdates = this.convertBusinessProfileToFirestore(updates as BusinessProfile);
      await updateDoc(docRef, { ...firestoreUpdates, updatedAt: Timestamp.now() });
      
      // Update local state
      const currentProfile = this.selectedBusinessProfileSubject.value;
      if (currentProfile && currentProfile.id === businessId) {
        const updatedProfile = { ...currentProfile, ...updates, updatedAt: new Date() };
        this.selectedBusinessProfileSubject.next(updatedProfile);
      }
    } catch (error) {
      console.error('Error updating business profile:', error);
      throw error;
    }
  }

  /**
   * Get current business status (open/closed)
   */
  getCurrentBusinessStatus(businessProfile: BusinessProfile): BusinessStatus {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentTime = now.toTimeString().slice(0, 5); // HH:mm format
    
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const todayHours = businessProfile.hoursOfOperation[dayNames[currentDay] as keyof HoursOfOperation] as DayHours;
    
    const isCurrentlyOpen = this.isBusinessOpen(todayHours, currentTime);
    const nextStatusChange = this.getNextStatusChange(businessProfile, now);
    
    return {
      isCurrentlyOpen,
      nextStatusChange,
      currentShift: this.getCurrentShift(todayHours, currentTime),
      staffOnDuty: 0, // TODO: Get from staff service
      estimatedCapacity: businessProfile.operationalSettings.maxOccupancy || 0
    };
  }

  /**
   * Check if business is currently open
   */
  private isBusinessOpen(dayHours: DayHours, currentTime: string): boolean {
    if (!dayHours.isOpen || !dayHours.openTime || !dayHours.closeTime) {
      return false;
    }
    
    const openTime = dayHours.openTime;
    const closeTime = dayHours.closeTime;
    
    // Handle overnight shifts (close time is next day)
    if (closeTime < openTime) {
      return currentTime >= openTime || currentTime <= closeTime;
    } else {
      return currentTime >= openTime && currentTime <= closeTime;
    }
  }

  /**
   * Get the current shift name if business has multiple shifts
   */
  private getCurrentShift(dayHours: DayHours, currentTime: string): string | undefined {
    if (!dayHours.shifts || dayHours.shifts.length === 0) {
      return undefined;
    }
    
    for (const shift of dayHours.shifts) {
      if (this.isTimeInRange(currentTime, shift.openTime, shift.closeTime)) {
        return shift.name;
      }
    }
    
    return undefined;
  }

  /**
   * Check if a time is within a range
   */
  private isTimeInRange(time: string, startTime: string, endTime: string): boolean {
    if (endTime < startTime) {
      // Overnight shift
      return time >= startTime || time <= endTime;
    } else {
      return time >= startTime && time <= endTime;
    }
  }

  /**
   * Get the next status change time (when business opens/closes next)
   */
  private getNextStatusChange(businessProfile: BusinessProfile, currentDate: Date): Date {
    // TODO: Implement logic to find next open/close time
    // This is a simplified version
    const tomorrow = new Date(currentDate);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(9, 0, 0, 0); // Default to 9 AM tomorrow
    return tomorrow;
  }

  /**
   * Create a default business profile
   */
  private createDefaultBusinessProfile(businessId: string): BusinessProfile {
    const now = new Date();
    
    const defaultDayHours: DayHours = {
      isOpen: true,
      openTime: '09:00',
      closeTime: '17:00'
    };
    
    return {
      id: businessId,
      name: 'My Business',
      businessType: 'retail',
      industry: 'General',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'US'
      },
      hoursOfOperation: {
        monday: defaultDayHours,
        tuesday: defaultDayHours,
        wednesday: defaultDayHours,
        thursday: defaultDayHours,
        friday: defaultDayHours,
        saturday: { ...defaultDayHours, openTime: '10:00', closeTime: '16:00' },
        sunday: { isOpen: false },
        holidays: [],
        specialDates: [],
        displaySettings: {
          showOnWebsite: true,
          showToCustomers: true,
          autoUpdateStatus: true
        }
      },
      hoursOfBusiness: {
        operationalHours: {
          monday: { ...defaultDayHours, openTime: '08:00', closeTime: '18:00' },
          tuesday: { ...defaultDayHours, openTime: '08:00', closeTime: '18:00' },
          wednesday: { ...defaultDayHours, openTime: '08:00', closeTime: '18:00' },
          thursday: { ...defaultDayHours, openTime: '08:00', closeTime: '18:00' },
          friday: { ...defaultDayHours, openTime: '08:00', closeTime: '18:00' },
          saturday: { ...defaultDayHours, openTime: '08:00', closeTime: '18:00' },
          sunday: { isOpen: false }
        },
        staffSchedulingWindow: {
          earliestStart: '06:00',
          latestEnd: '22:00',
          allowOvernightShifts: false
        }
      },
      operationalSettings: {
        breakPolicies: {
          minimumShiftForBreak: 4,
          breakDuration: 15,
          mealBreakDuration: 30,
          minimumShiftForMeal: 6,
          maxHoursWithoutBreak: 5
        },
        overtimePolicies: {
          dailyOvertimeThreshold: 8,
          weeklyOvertimeThreshold: 40,
          overtimeMultiplier: 1.5,
          requireApprovalForOvertime: true
        }
      },
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      locale: 'en-US',
      currency: 'USD',
      staffSettings: {
        scheduleAdvanceNotice: 7,
        allowStaffSelfScheduling: false,
        allowShiftSwapping: true,
        requireManagerApproval: true,
        clockInMethod: 'manual',
        allowEarlyClockIn: true,
        earlyClockInThreshold: 15,
        allowLateClockOut: true,
        lateClockOutThreshold: 15,
        minimumAvailabilityHours: 20,
        requireWeekendAvailability: false,
        requireHolidayAvailability: false
      },
      schedulingSettings: {
        enableAutoScheduling: false,
        autoSchedulingRules: [],
        defaultShiftLength: 8,
        minimumShiftLength: 2,
        maximumShiftLength: 12,
        minimumCoverageRules: [],
        schedulePublishDay: 0, // Sunday
        notifyStaffOfChanges: true,
        notificationLeadTime: 24
      },
      compliance: {
        laborLaws: {
          jurisdiction: 'US-Federal',
          minimumWage: 7.25,
          overtimeRules: {},
          breakRequirements: {}
        },
        recordRetention: {
          timesheetRetentionDays: 1095, // 3 years
          scheduleRetentionDays: 365, // 1 year
          performanceRetentionDays: 2555 // 7 years
        }
      },
      createdAt: now,
      updatedAt: now,
      createdBy: 'system',
      ownerId: 'current-user', // TODO: Get from auth service
      status: 'active'
    };
  }

  /**
   * Convert Firestore data to BusinessProfile
   */
  private convertFirestoreToBusinessProfile(data: any): BusinessProfile {
    return {
      ...data,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date()
    };
  }

  /**
   * Convert BusinessProfile to Firestore data
   */
  private convertBusinessProfileToFirestore(businessProfile: BusinessProfile): any {
    return {
      ...businessProfile,
      createdAt: Timestamp.fromDate(businessProfile.createdAt),
      updatedAt: Timestamp.fromDate(businessProfile.updatedAt)
    };
  }
}
