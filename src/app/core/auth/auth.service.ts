import { Injectable, inject } from '@angular/core';
import { Auth, User, user, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, GoogleAuthProvider, signInWithPopup, updateProfile } from '@angular/fire/auth';
import { Firestore, doc, setDoc, getDoc, updateDoc } from '@angular/fire/firestore';
import { Observable, from, map, switchMap, of } from 'rxjs';

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: 'admin' | 'manager' | 'staff';
  staffId?: string;
  businessIds: string[];
  primaryBusinessId: string;
  createdAt: Date;
  lastLoginAt: Date;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private auth = inject(Auth);
  private firestore = inject(Firestore);
  
  // Current user observable
  user$ = user(this.auth);
  
  // Current user profile with additional data
  userProfile$: Observable<UserProfile | null> = this.user$.pipe(
    switchMap(user => {
      if (!user) return of(null);
      return this.getUserProfile(user.uid);
    })
  );

  constructor() {}

  // Sign in with email and password
  signInWithEmail(email: string, password: string): Observable<UserProfile | null> {
    return from(signInWithEmailAndPassword(this.auth, email, password)).pipe(
      switchMap(credential => {
        this.updateLastLogin(credential.user.uid);
        return this.getUserProfile(credential.user.uid);
      })
    );
  }

  // Sign up with email and password
  signUpWithEmail(email: string, password: string, displayName: string, role: 'admin' | 'manager' | 'staff' = 'staff'): Observable<UserProfile | null> {
    return from(createUserWithEmailAndPassword(this.auth, email, password)).pipe(
      switchMap(credential => {
        // Update the user's display name
        return from(updateProfile(credential.user, { displayName })).pipe(
          switchMap(() => {
            // Create user profile in Firestore
            const userProfile: UserProfile = {
              uid: credential.user.uid,
              email: credential.user.email!,
              displayName,
              role,
              businessIds: [],
              primaryBusinessId: '',
              createdAt: new Date(),
              lastLoginAt: new Date()
            };
            return this.createUserProfile(userProfile);
          })
        );
      })
    );
  }

  // Sign in with Google
  signInWithGoogle(): Observable<UserProfile | null> {
    const provider = new GoogleAuthProvider();
    return from(signInWithPopup(this.auth, provider)).pipe(
      switchMap(credential => {
        this.updateLastLogin(credential.user.uid);
        return this.getUserProfile(credential.user.uid).pipe(
          switchMap(profile => {
            if (!profile) {
              // Create new profile for Google user
              const newProfile: UserProfile = {
                uid: credential.user.uid,
                email: credential.user.email!,
                displayName: credential.user.displayName || 'User',
                photoURL: credential.user.photoURL || undefined,
                role: 'staff',
                businessIds: [],
                primaryBusinessId: '',
                createdAt: new Date(),
                lastLoginAt: new Date()
              };
              return this.createUserProfile(newProfile);
            }
            return of(profile);
          })
        );
      })
    );
  }

  // Sign out
  signOut(): Observable<void> {
    return from(signOut(this.auth));
  }

  // Get user profile from Firestore
  private getUserProfile(uid: string): Observable<UserProfile | null> {
    const userDoc = doc(this.firestore, `users/${uid}`);
    return from(getDoc(userDoc)).pipe(
      map(docSnap => {
        if (docSnap.exists()) {
          const data = docSnap.data();
          return {
            ...data,
            createdAt: data['createdAt']?.toDate() || new Date(),
            lastLoginAt: data['lastLoginAt']?.toDate() || new Date()
          } as UserProfile;
        }
        return null;
      })
    );
  }

  // Create user profile in Firestore
  private createUserProfile(profile: UserProfile): Observable<UserProfile> {
    const userDoc = doc(this.firestore, `users/${profile.uid}`);
    return from(setDoc(userDoc, profile)).pipe(
      map(() => profile)
    );
  }

  // Update user profile
  updateUserProfile(uid: string, updates: Partial<UserProfile>): Observable<void> {
    const userDoc = doc(this.firestore, `users/${uid}`);
    return from(updateDoc(userDoc, updates));
  }

  // Update last login timestamp
  private updateLastLogin(uid: string): void {
    const userDoc = doc(this.firestore, `users/${uid}`);
    updateDoc(userDoc, { lastLoginAt: new Date() }).catch(console.error);
  }

  // Check if user has specific role
  hasRole(role: 'admin' | 'manager' | 'staff'): Observable<boolean> {
    return this.userProfile$.pipe(
      map(profile => profile?.role === role || false)
    );
  }

  // Check if user has admin or manager role
  isAdminOrManager(): Observable<boolean> {
    return this.userProfile$.pipe(
      map(profile => profile?.role === 'admin' || profile?.role === 'manager' || false)
    );
  }
}
